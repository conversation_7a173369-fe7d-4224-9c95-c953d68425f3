# Invoice Generator API

An open-source API service that allows freelancers and small businesses to generate professional invoices in PDF format without requiring authentication.

## Features

- 🚀 **No Authentication Required** - Generate invoices instantly
- 📄 **PDF Generation** - Professional PDF invoices with HTML templates
- ☁️ **Cloud Storage** - Secure temporary storage with Cloudflare R2
- ⚡ **Rate Limited** - Fair usage with 5 requests per minute per IP
- 🔒 **Secure** - Input validation and security best practices
- 🐳 **Docker Ready** - Easy deployment with Docker and Docker Compose
- 📊 **Monitoring** - Health checks and comprehensive logging

## Quick Start

### Using Docker Compose (Recommended)

1. Clone the repository:

```bash
git clone <repository-url>
cd invoice-generator-api
```

2. Copy the environment file:

```bash
cp .env.example .env
```

3. Update the `.env` file with your Cloudflare R2 credentials:

```env
R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
R2_ACCESS_KEY=your-access-key
R2_SECRET_KEY=your-secret-key
R2_BUCKET_NAME=invoice-pdfs
```

4. Start the services:

```bash
docker-compose up -d
```

5. The API will be available at `http://localhost:3000`

### Local Development

1. Install dependencies:

   - Rust (latest stable)
   - PostgreSQL
   - wkhtmltopdf

2. Set up the database:

```bash
createdb invoice_generator
```

3. Copy and configure environment variables:

```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Run the application:

```bash
cargo run
```

## API Documentation

### Generate Invoice

**POST** `/api/v1/invoices`

Generate a new invoice PDF.

**Request Body:**

```json
{
  "template_id": "default",
  "invoice_date": "2024-01-15",
  "due_date": "2024-02-15",
  "currency": "USD",
  "tax": 10.0,
  "gst": 5.0,
  "shipping_fee": 25.0,
  "discount": {
    "percentage": 10.0
  },
  "billed_to": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "address": {
      "street": "123 Main St",
      "city": "New York",
      "state": "NY",
      "post_code": "10001",
      "country": "USA"
    }
  },
  "from": {
    "name": "Your Company",
    "email": "<EMAIL>",
    "phone": "+1987654321",
    "address": {
      "street": "456 Business Ave",
      "city": "San Francisco",
      "state": "CA",
      "post_code": "94105",
      "country": "USA"
    }
  },
  "items": [
    {
      "name": "Web Development",
      "description": "Frontend development services",
      "quantity": 40,
      "price": 75.0
    },
    {
      "name": "Hosting Setup",
      "description": "Server configuration and deployment",
      "quantity": 1,
      "price": 200.0
    }
  ]
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "invoice_id": "550e8400-e29b-41d4-a716-446655440000",
    "download_url": "https://r2.cloudflarestorage.com/...",
    "expires_at": "2024-01-22T12:00:00Z",
    "file_size": 245760,
    "created_at": "2024-01-15T12:00:00Z"
  }
}
```

### Health Check

**GET** `/health`

Check the service health status.

**Response:**

```json
{
  "status": "healthy",
  "service": "invoice-generator-api",
  "version": "0.1.0",
  "timestamp": "2024-01-15T12:00:00Z"
}
```

### List Templates

**GET** `/api/v1/templates`

Get available invoice templates.

**Response:**

```json
{
  "success": true,
  "data": {
    "templates": [
      {
        "id": "default",
        "name": "Default Template",
        "description": "Standard invoice template",
        "preview_url": null
      }
    ]
  }
}
```

## Configuration

### Environment Variables

| Variable                    | Description                   | Default                                    |
| --------------------------- | ----------------------------- | ------------------------------------------ |
| `PORT`                      | Server port                   | `3000`                                     |
| `DATABASE_URL`              | PostgreSQL connection string  | `postgresql://localhost/invoice_generator` |
| `R2_ENDPOINT`               | Cloudflare R2 endpoint        | Required                                   |
| `R2_ACCESS_KEY`             | R2 access key                 | Required                                   |
| `R2_SECRET_KEY`             | R2 secret key                 | Required                                   |
| `R2_BUCKET_NAME`            | R2 bucket name                | `invoice-pdfs`                             |
| `R2_REGION`                 | R2 region                     | `auto`                                     |
| `RATE_LIMIT_REQUESTS`       | Requests per minute per IP    | `5`                                        |
| `RATE_LIMIT_WINDOW_SECONDS` | Rate limit window             | `60`                                       |
| `PDF_RETENTION_DAYS`        | PDF retention period          | `7`                                        |
| `MAX_REQUEST_SIZE`          | Maximum request size in bytes | `1048576`                                  |
| `WKHTMLTOPDF_PATH`          | Custom wkhtmltopdf path       | Auto-detect                                |
| `RUST_LOG`                  | Logging level                 | `info`                                     |

### Security Configuration

| Variable                  | Description                    | Default |
| ------------------------- | ------------------------------ | ------- |
| `ENABLE_HSTS`             | Enable HSTS security headers   | `true`  |
| `ENABLE_CSP`              | Enable Content Security Policy | `true`  |
| `REQUEST_TIMEOUT_SECONDS` | Request timeout in seconds     | `30`    |
| `ALLOWED_ORIGINS`         | CORS allowed origins           | `*`     |
| `ENABLE_SECURITY_HEADERS` | Enable security headers        | `true`  |

### Monitoring Configuration

| Variable                   | Description                    | Default |
| -------------------------- | ------------------------------ | ------- |
| `ENABLE_METRICS`           | Enable Prometheus metrics      | `true`  |
| `ENABLE_TRACING`           | Enable structured logging      | `true`  |
| `LOG_LEVEL`                | Application log level          | `info`  |
| `ENABLE_CORRELATION_IDS`   | Enable request correlation IDs | `true`  |
| `ENABLE_REQUEST_LOGGING`   | Enable request/response logs   | `true`  |
| `METRICS_ENDPOINT_ENABLED` | Expose /metrics endpoint       | `true`  |

## Architecture

### System Overview

The Invoice Generator API follows a layered architecture pattern with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────┐
│                    HTTP Layer (Axum)                       │
├─────────────────────────────────────────────────────────────┤
│  Middleware: Security, Rate Limiting, Logging, Monitoring  │
├─────────────────────────────────────────────────────────────┤
│                     Handlers Layer                         │
├─────────────────────────────────────────────────────────────┤
│                    Services Layer                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌────────┐ │
│  │   Invoice   │ │  Template   │ │   Storage   │ │  Rate  │ │
│  │  Service    │ │  Service    │ │  Service    │ │ Limit  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └────────┘ │
├─────────────────────────────────────────────────────────────┤
│                  Repository Layer                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   Invoice   │ │ Invoice Item│ │ Rate Limit  │           │
│  │ Repository  │ │ Repository  │ │ Repository  │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                    Data Layer                              │
│  ┌─────────────┐                   ┌─────────────┐         │
│  │ PostgreSQL  │                   │ Cloudflare  │         │
│  │  Database   │                   │     R2      │         │
│  └─────────────┘                   └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

### Core Components

- **Handlers**: Process HTTP requests and responses
- **Services**: Contain business logic and orchestrate operations
- **Repositories**: Abstract data access and database operations
- **Middleware**: Cross-cutting concerns (security, logging, rate limiting)
- **Background Tasks**: Scheduled cleanup and maintenance operations

### Data Flow

1. **Request Processing**: HTTP request → Middleware → Handler
2. **Business Logic**: Handler → Service → Repository
3. **PDF Generation**: Template Service → PDF Service → Storage Service
4. **Response**: Storage URL → Handler → HTTP response

## Development

### Project Structure

```
src/
├── main.rs              # Application entry point
├── config.rs            # Configuration management
├── database/            # Database connection and migrations
├── handlers/            # HTTP request handlers
├── models.rs            # Data structures and validation
├── services/            # Business logic services
└── utils.rs             # Utility functions
```

### Adding New Features

1. **Database Changes**: Add migrations in `src/database/migrations/`
2. **API Endpoints**: Add handlers in `src/handlers/`
3. **Business Logic**: Add services in `src/services/`
4. **Data Models**: Update `src/models.rs`

### Running Tests

```bash
cargo test
```

### Code Formatting

```bash
cargo fmt
```

### Linting

```bash
cargo clippy
```

## Deployment

### Docker

Build and run with Docker:

```bash
docker build -t invoice-generator-api .
docker run -p 3000:3000 --env-file .env invoice-generator-api
```

### Production Considerations

1. **Environment Variables**: Use secure methods to manage secrets
2. **Database**: Use a managed PostgreSQL service with connection pooling
3. **Storage**: Configure Cloudflare R2 with proper access controls
4. **Monitoring**: Set up logging, metrics, and alerting
5. **SSL/TLS**: Use HTTPS in production with proper certificates
6. **Rate Limiting**: Adjust rate limits based on your traffic patterns
7. **Resource Limits**: Configure appropriate CPU and memory limits
8. **Health Checks**: Set up health check endpoints for load balancers
9. **Backup Strategy**: Implement database backup and recovery procedures

### Performance Optimization

- **Database Connection Pooling**: Configured with optimal pool sizes
- **Async Operations**: All I/O operations are asynchronous
- **Efficient PDF Generation**: Optimized HTML templates and PDF settings
- **File Storage**: Direct uploads to R2 with pre-signed URLs
- **Caching**: HTTP response caching headers for static content
- **Compression**: Gzip compression for API responses

### Monitoring Endpoints

- `GET /health` - Basic health check
- `GET /metrics` - Prometheus metrics (if enabled)
- Database connection status and pool metrics
- R2 storage connectivity and operation metrics
- PDF generation performance metrics

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions, please open an issue on GitHub.

## Roadmap

### ✅ Completed Features

- [x] **Core Infrastructure**

  - [x] Rust + Axum web framework
  - [x] PostgreSQL database with Sea ORM
  - [x] Docker containerization
  - [x] Environment configuration management

- [x] **Invoice Generation**

  - [x] PDF generation with HTML templates
  - [x] Template rendering system with Handlebars
  - [x] Professional invoice layout with CSS styling
  - [x] Multi-page PDF support

- [x] **Storage & File Management**

  - [x] Cloudflare R2 integration
  - [x] Pre-signed URL generation
  - [x] Automatic file cleanup (7-day retention)
  - [x] Organized file structure by date

- [x] **Security & Rate Limiting**

  - [x] IP-based rate limiting (5 requests/minute)
  - [x] Input validation and sanitization
  - [x] Security headers and CORS
  - [x] Request size limits

- [x] **Monitoring & Observability**

  - [x] Structured logging with correlation IDs
  - [x] Prometheus metrics collection
  - [x] Health check endpoints
  - [x] Request/response logging middleware

- [x] **Background Tasks**

  - [x] Scheduled cleanup tasks
  - [x] Expired invoice removal
  - [x] Rate limit cleanup
  - [x] Task scheduler with configurable intervals

- [x] **Documentation**
  - [x] Comprehensive API documentation
  - [x] Database schema documentation
  - [x] Deployment guides
  - [x] Architecture documentation

### 🚧 In Progress

- [ ] **Enhanced Testing**
  - [ ] Integration test coverage
  - [ ] Performance testing
  - [ ] Load testing scenarios

### 🔮 Future Enhancements

- [ ] **Template System**

  - [ ] Multiple template support
  - [ ] Template preview functionality
  - [ ] Custom template uploads

- [ ] **Advanced Features**

  - [ ] Webhook notifications
  - [ ] Invoice analytics dashboard
  - [ ] Multi-currency support enhancements
  - [ ] Invoice versioning

- [ ] **Performance & Scaling**

  - [ ] Redis caching layer
  - [ ] Database read replicas
  - [ ] CDN integration
  - [ ] Horizontal scaling optimizations

- [ ] **Developer Experience**
  - [ ] OpenAPI/Swagger documentation
  - [ ] SDK generation for popular languages
  - [ ] Postman collection
  - [ ] Interactive API explorer
