use serde::{Deserialize, Serialize};
use validator::Validate;
use chrono::NaiveDate;

/// Main invoice request structure matching the TypeScript interface from PRD
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
#[serde(rename_all = "camelCase")]
pub struct InvoiceRequest {
    /// Template ID for the invoice (optional, defaults to "default")
    pub template_id: Option<String>,
    
    /// Invoice date (required)
    #[validate(custom = "validate_date")]
    pub invoice_date: NaiveDate,
    
    /// Due date (optional)
    #[validate(custom(function = "validate_date"))]
    pub due_date: Option<NaiveDate>,
    
    /// Currency code (required, 3-letter ISO 4217 code)
    #[validate(length(min = 3, max = 3, message = "Currency must be a 3-letter ISO code"))]
    #[validate(custom = "validate_currency_code")]
    pub currency: String,
    
    /// Tax percentage (optional, non-negative)
    #[validate(range(min = 0.0, max = 100.0, message = "Tax must be between 0 and 100 percent"))]
    pub tax: Option<f64>,
    
    /// GST percentage (optional, non-negative)
    #[validate(range(min = 0.0, max = 100.0, message = "GST must be between 0 and 100 percent"))]
    pub gst: Option<f64>,
    
    /// Shipping fee (optional, non-negative)
    #[validate(range(min = 0.0, message = "Shipping fee must be non-negative"))]
    pub shipping_fee: Option<f64>,
    
    /// Discount information (optional)
    #[validate]
    pub discount: Option<Discount>,
    
    /// Billing contact information (required)
    #[validate]
    pub billed_to: Contact,
    
    /// Sender contact information (required)
    #[validate]
    pub from: Contact,
    
    /// List of invoice items (required, at least one item)
    #[validate(length(min = 1, message = "At least one item is required"))]
    pub items: Vec<InvoiceItem>,
}

/// Discount structure supporting both percentage and fixed amount discounts
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct Discount {
    /// Percentage discount (0-100)
    #[validate(range(min = 0.0, max = 100.0, message = "Percentage must be between 0 and 100"))]
    pub percentage: Option<f64>,
    
    /// Fixed price discount
    #[validate(range(min = 0.0, message = "Price must be non-negative"))]
    pub price: Option<f64>,
}

/// Contact information structure for billing and sender details
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct Contact {
    /// Contact name (required, max 255 characters)
    #[validate(length(min = 1, max = 255, message = "Name is required and must be less than 255 characters"))]
    pub name: String,
    
    /// Email address (optional, valid email format)
    #[validate(email(message = "Invalid email format"))]
    pub email: Option<String>,
    
    /// Phone number (optional, max 50 characters)
    #[validate(length(max = 50, message = "Phone number must be less than 50 characters"))]
    pub phone: Option<String>,
    
    /// ABN/Tax ID (optional, max 50 characters)
    #[validate(length(max = 50, message = "ABN must be less than 50 characters"))]
    pub abn: Option<String>,
    
    /// Address information (optional)
    #[validate]
    pub address: Option<Address>,
}

/// Address structure for contact information
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
#[serde(rename_all = "camelCase")]
pub struct Address {
    /// Street address (required, max 255 characters)
    #[validate(length(min = 1, max = 255, message = "Street is required and must be less than 255 characters"))]
    pub street: String,
    
    /// State/Province (required, max 100 characters)
    #[validate(length(min = 1, max = 100, message = "State is required and must be less than 100 characters"))]
    pub state: String,
    
    /// City (required, max 100 characters)
    #[validate(length(min = 1, max = 100, message = "City is required and must be less than 100 characters"))]
    pub city: String,
    
    /// Postal/ZIP code (required, max 20 characters)
    #[validate(length(min = 1, max = 20, message = "Post code is required and must be less than 20 characters"))]
    pub post_code: String,
    
    /// Country (required, max 100 characters)
    #[validate(length(min = 1, max = 100, message = "Country is required and must be less than 100 characters"))]
    pub country: String,
}

/// Individual invoice item structure
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct InvoiceItem {
    /// Item name (required, max 255 characters)
    #[validate(length(min = 1, max = 255, message = "Item name is required and must be less than 255 characters"))]
    pub name: String,
    
    /// Item description (optional, max 1000 characters)
    #[validate(length(max = 1000, message = "Description must be less than 1000 characters"))]
    pub description: Option<String>,
    
    /// Quantity (required, minimum 1)
    #[validate(range(min = 1, message = "Quantity must be at least 1"))]
    pub quantity: i32,
    
    /// Unit price (required, non-negative)
    #[validate(range(min = 0.0, message = "Price must be non-negative"))]
    pub price: f64,
}

// Custom validation functions

/// Validates that the invoice date is within reasonable bounds
fn validate_date(date: &NaiveDate) -> Result<(), validator::ValidationError> {
    // Allow dates from 1900 to 100 years in the future
    let min_date = NaiveDate::from_ymd_opt(1900, 1, 1).unwrap();
    let max_date = chrono::Utc::now().date_naive() + chrono::Duration::days(36500); // ~100 years
    
    if *date < min_date || *date > max_date {
        let mut error = validator::ValidationError::new("invalid_date");
        error.message = Some("Date must be between 1900 and 100 years in the future".into());
        return Err(error);
    }
    
    Ok(())
}



/// Validates currency code against ISO 4217 standard
fn validate_currency_code(currency: &str) -> Result<(), validator::ValidationError> {
    // Common ISO 4217 currency codes
    const VALID_CURRENCIES: &[&str] = &[
        "USD", "EUR", "GBP", "JPY", "AUD", "CAD", "CHF", "CNY", "SEK", "NZD",
        "MXN", "SGD", "HKD", "NOK", "ZAR", "TRY", "BRL", "INR", "KRW", "PLN",
        "THB", "IDR", "HUF", "CZK", "ILS", "CLP", "PHP", "AED", "COP", "SAR",
        "MYR", "RON", "BGN", "HRK", "RUB", "UAH", "EGP", "QAR", "KWD", "BHD",
        "OMR", "JOD", "LBP", "TND", "DZD", "MAD", "KES", "UGX", "TZS", "ZMW",
        "BWP", "MUR", "SCR", "MGA", "XOF", "XAF", "GHS", "NGN", "ETB", "RWF"
    ];
    
    if !VALID_CURRENCIES.contains(&currency) {
        let mut error = validator::ValidationError::new("invalid_currency");
        error.message = Some("Invalid ISO 4217 currency code".into());
        return Err(error);
    }
    
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use chrono::NaiveDate;
    
    fn create_valid_contact() -> Contact {
        Contact {
            name: "John Doe".to_string(),
            email: Some("<EMAIL>".to_string()),
            phone: Some("+1234567890".to_string()),
            abn: Some("***********".to_string()),
            address: Some(Address {
                street: "123 Main St".to_string(),
                state: "CA".to_string(),
                city: "San Francisco".to_string(),
                post_code: "94102".to_string(),
                country: "USA".to_string(),
            }),
        }
    }
    
    fn create_valid_invoice_item() -> InvoiceItem {
        InvoiceItem {
            name: "Test Item".to_string(),
            description: Some("Test description".to_string()),
            quantity: 1,
            price: 100.0,
        }
    }
    
    #[test]
    fn test_valid_invoice_request() {
        let request = InvoiceRequest {
            template_id: Some("default".to_string()),
            invoice_date: NaiveDate::from_ymd_opt(2024, 1, 1).unwrap(),
            due_date: Some(NaiveDate::from_ymd_opt(2024, 1, 31).unwrap()),
            currency: "USD".to_string(),
            tax: Some(10.0),
            gst: Some(5.0),
            shipping_fee: Some(15.0),
            discount: Some(Discount {
                percentage: Some(5.0),
                price: None,
            }),
            billed_to: create_valid_contact(),
            from: create_valid_contact(),
            items: vec![create_valid_invoice_item()],
        };
        
        assert!(request.validate().is_ok());
    }
    
    #[test]
    fn test_invalid_currency_code() {
        let mut request = InvoiceRequest {
            template_id: None,
            invoice_date: NaiveDate::from_ymd_opt(2024, 1, 1).unwrap(),
            due_date: None,
            currency: "INVALID".to_string(),
            tax: None,
            gst: None,
            shipping_fee: None,
            discount: None,
            billed_to: create_valid_contact(),
            from: create_valid_contact(),
            items: vec![create_valid_invoice_item()],
        };
        
        assert!(request.validate().is_err());
        
        // Test with valid currency
        request.currency = "USD".to_string();
        assert!(request.validate().is_ok());
    }
    
    #[test]
    fn test_empty_items_validation() {
        let request = InvoiceRequest {
            template_id: None,
            invoice_date: NaiveDate::from_ymd_opt(2024, 1, 1).unwrap(),
            due_date: None,
            currency: "USD".to_string(),
            tax: None,
            gst: None,
            shipping_fee: None,
            discount: None,
            billed_to: create_valid_contact(),
            from: create_valid_contact(),
            items: vec![], // Empty items should fail validation
        };
        
        assert!(request.validate().is_err());
    }
    
    #[test]
    fn test_discount_validation() {
        // Valid percentage discount
        let discount = Discount {
            percentage: Some(10.0),
            price: None,
        };
        assert!(discount.validate().is_ok());
        
        // Valid price discount
        let discount = Discount {
            percentage: None,
            price: Some(50.0),
        };
        assert!(discount.validate().is_ok());
        
        // Invalid percentage (over 100)
        let discount = Discount {
            percentage: Some(150.0),
            price: None,
        };
        assert!(discount.validate().is_err());
        
        // Invalid negative price
        let discount = Discount {
            percentage: None,
            price: Some(-10.0),
        };
        assert!(discount.validate().is_err());
    }
} 