use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

/// Main response structure for invoice generation API
#[derive(Debug, <PERSON>lone, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct InvoiceResponse {
    /// Indicates if the operation was successful
    pub success: bool,
    
    /// Invoice data when successful
    pub data: Option<InvoiceData>,
    
    /// Error information when unsuccessful
    pub error: Option<ErrorData>,
}

/// Invoice data returned on successful generation
#[derive(Debug, <PERSON>lone, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct InvoiceData {
    /// Unique identifier for the generated invoice
    pub invoice_id: String,
    
    /// URL to download the generated PDF
    pub download_url: String,
    
    /// Timestamp when the download URL expires
    pub expires_at: DateTime<Utc>,
    
    /// Size of the generated PDF file in bytes
    pub file_size: u64,
    
    /// Timestamp when the invoice was created
    pub created_at: DateTime<Utc>,
}

/// Fallback invoice data when storage service is unavailable
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct FallbackInvoiceData {
    /// Unique identifier for the generated invoice
    pub invoice_id: String,
    
    /// Base64 encoded PDF data
    pub pdf_data: String,
    
    /// Indicates this response is in fallback mode
    pub fallback_mode: bool,
    
    /// Message explaining the fallback mode
    pub message: String,
    
    /// Timestamp when the invoice was created
    pub created_at: DateTime<Utc>,
}

/// Error information structure
#[derive(Debug, Clone, Serialize)]
pub struct ErrorData {
    /// Error code for programmatic handling
    pub code: String,
    
    /// Human-readable error message
    pub message: String,
    
    /// Optional additional error details
    pub details: Option<String>,
}

/// Template information structure
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct TemplateInfo {
    /// Unique template identifier
    pub id: String,

    /// Human-readable template name
    pub name: String,

    /// Template description
    pub description: String,

    /// Optional URL to preview the template
    pub preview_url: Option<String>,
}

/// Health check response structure
#[derive(Debug, Clone, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct HealthCheckResponse {
    /// Overall service status (e.g., "healthy", "degraded", "unhealthy")
    pub status: String,
    
    /// Service name
    pub service: String,
    
    /// Service version
    pub version: String,
    
    /// Timestamp of the health check
    pub timestamp: DateTime<Utc>,
    
    /// Database connection status
    pub database: String,
    
    /// Storage service status
    pub storage: String,
}

// Convenience constructors for common response patterns
impl InvoiceResponse {
    /// Create a successful response with invoice data
    pub fn success(data: InvoiceData) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
        }
    }
    
    /// Create a successful response with fallback data
    pub fn success_fallback(data: FallbackInvoiceData) -> Self {
        // Convert FallbackInvoiceData to InvoiceData format for consistency
        let invoice_data = InvoiceData {
            invoice_id: data.invoice_id,
            download_url: String::new(), // No download URL in fallback mode
            expires_at: data.created_at, // Set to creation time
            file_size: 0, // Size not available in fallback mode
            created_at: data.created_at,
        };
        
        Self {
            success: true,
            data: Some(invoice_data),
            error: None,
        }
    }
    
    /// Create an error response
    pub fn error(error: ErrorData) -> Self {
        Self {
            success: false,
            data: None,
            error: Some(error),
        }
    }
}

impl ErrorData {
    /// Create a new error with code and message
    pub fn new(code: impl Into<String>, message: impl Into<String>) -> Self {
        Self {
            code: code.into(),
            message: message.into(),
            details: None,
        }
    }
    
    /// Create a new error with code, message, and details
    pub fn with_details(
        code: impl Into<String>, 
        message: impl Into<String>, 
        details: impl Into<String>
    ) -> Self {
        Self {
            code: code.into(),
            message: message.into(),
            details: Some(details.into()),
        }
    }
    
    /// Create a validation error
    pub fn validation(message: impl Into<String>) -> Self {
        Self::new("VALIDATION_ERROR", message)
    }
    
    /// Create an internal server error
    pub fn internal(message: impl Into<String>) -> Self {
        Self::new("INTERNAL_ERROR", message)
    }
    
    /// Create a rate limit error
    pub fn rate_limit(message: impl Into<String>) -> Self {
        Self::new("RATE_LIMIT_EXCEEDED", message)
    }
    
    /// Create a storage error
    pub fn storage(message: impl Into<String>) -> Self {
        Self::new("STORAGE_ERROR", message)
    }
    
    /// Create a PDF generation error
    pub fn pdf_generation(message: impl Into<String>) -> Self {
        Self::new("PDF_GENERATION_ERROR", message)
    }
}

impl HealthCheckResponse {
    /// Create a healthy response
    pub fn healthy(service: impl Into<String>, version: impl Into<String>) -> Self {
        Self {
            status: "healthy".to_string(),
            service: service.into(),
            version: version.into(),
            timestamp: Utc::now(),
            database: "healthy".to_string(),
            storage: "healthy".to_string(),
        }
    }
    
    /// Create a degraded response
    pub fn degraded(
        service: impl Into<String>, 
        version: impl Into<String>,
        database_status: impl Into<String>,
        storage_status: impl Into<String>
    ) -> Self {
        Self {
            status: "degraded".to_string(),
            service: service.into(),
            version: version.into(),
            timestamp: Utc::now(),
            database: database_status.into(),
            storage: storage_status.into(),
        }
    }
    
    /// Create an unhealthy response
    pub fn unhealthy(
        service: impl Into<String>, 
        version: impl Into<String>,
        database_status: impl Into<String>,
        storage_status: impl Into<String>
    ) -> Self {
        Self {
            status: "unhealthy".to_string(),
            service: service.into(),
            version: version.into(),
            timestamp: Utc::now(),
            database: database_status.into(),
            storage: storage_status.into(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use chrono::Utc;
    
    #[test]
    fn test_invoice_response_success() {
        let data = InvoiceData {
            invoice_id: "inv_123".to_string(),
            download_url: "https://example.com/download/inv_123.pdf".to_string(),
            expires_at: Utc::now(),
            file_size: 1024,
            created_at: Utc::now(),
        };
        
        let response = InvoiceResponse::success(data.clone());
        
        assert!(response.success);
        assert!(response.data.is_some());
        assert!(response.error.is_none());
        assert_eq!(response.data.unwrap().invoice_id, "inv_123");
    }
    
    #[test]
    fn test_invoice_response_error() {
        let error = ErrorData::validation("Invalid input data");
        let response = InvoiceResponse::error(error);
        
        assert!(!response.success);
        assert!(response.data.is_none());
        assert!(response.error.is_some());
        
        let error_data = response.error.unwrap();
        assert_eq!(error_data.code, "VALIDATION_ERROR");
        assert_eq!(error_data.message, "Invalid input data");
    }
    
    #[test]
    fn test_error_data_constructors() {
        // Test basic constructor
        let error = ErrorData::new("TEST_CODE", "Test message");
        assert_eq!(error.code, "TEST_CODE");
        assert_eq!(error.message, "Test message");
        assert!(error.details.is_none());
        
        // Test with details
        let error = ErrorData::with_details("TEST_CODE", "Test message", "Additional details");
        assert_eq!(error.code, "TEST_CODE");
        assert_eq!(error.message, "Test message");
        assert_eq!(error.details.unwrap(), "Additional details");
        
        // Test specific error types
        let validation_error = ErrorData::validation("Invalid field");
        assert_eq!(validation_error.code, "VALIDATION_ERROR");
        
        let internal_error = ErrorData::internal("Database connection failed");
        assert_eq!(internal_error.code, "INTERNAL_ERROR");
        
        let rate_limit_error = ErrorData::rate_limit("Too many requests");
        assert_eq!(rate_limit_error.code, "RATE_LIMIT_EXCEEDED");
        
        let storage_error = ErrorData::storage("S3 upload failed");
        assert_eq!(storage_error.code, "STORAGE_ERROR");
        
        let pdf_error = ErrorData::pdf_generation("wkhtmltopdf failed");
        assert_eq!(pdf_error.code, "PDF_GENERATION_ERROR");
    }
    
    #[test]
    fn test_health_check_response() {
        // Test healthy response
        let healthy = HealthCheckResponse::healthy("invoice-generator", "1.0.0");
        assert_eq!(healthy.status, "healthy");
        assert_eq!(healthy.service, "invoice-generator");
        assert_eq!(healthy.version, "1.0.0");
        assert_eq!(healthy.database, "healthy");
        assert_eq!(healthy.storage, "healthy");
        
        // Test degraded response
        let degraded = HealthCheckResponse::degraded(
            "invoice-generator", 
            "1.0.0",
            "healthy",
            "degraded"
        );
        assert_eq!(degraded.status, "degraded");
        assert_eq!(degraded.storage, "degraded");
        
        // Test unhealthy response
        let unhealthy = HealthCheckResponse::unhealthy(
            "invoice-generator", 
            "1.0.0",
            "unhealthy",
            "unhealthy"
        );
        assert_eq!(unhealthy.status, "unhealthy");
        assert_eq!(unhealthy.database, "unhealthy");
        assert_eq!(unhealthy.storage, "unhealthy");
    }
    
    #[test]
    fn test_template_info_serialization() {
        let template = TemplateInfo {
            id: "modern".to_string(),
            name: "Modern Template".to_string(),
            description: "A clean, modern invoice template".to_string(),
            preview_url: Some("https://example.com/preview/modern.png".to_string()),
        };
        
        let json = serde_json::to_string(&template).unwrap();
        assert!(json.contains("previewUrl")); // Check camelCase conversion
    }
    
    #[test]
    fn test_fallback_invoice_data() {
        let fallback = FallbackInvoiceData {
            invoice_id: "inv_fallback_123".to_string(),
            pdf_data: "base64encodeddata".to_string(),
            fallback_mode: true,
            message: "Storage service unavailable, returning PDF data directly".to_string(),
            created_at: Utc::now(),
        };
        
        assert_eq!(fallback.invoice_id, "inv_fallback_123");
        assert!(fallback.fallback_mode);
        assert!(!fallback.pdf_data.is_empty());
    }
} 