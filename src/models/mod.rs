pub mod request;
pub mod response;

// Re-export commonly used types for convenience
pub use request::*;
pub use response::*;

#[cfg(test)]
mod integration_tests {
    use super::*;
    use chrono::{DateTime, NaiveDate, Utc};
    use serde_json;

    #[test]
    fn test_request_serialization() {
        let request = InvoiceRequest {
            template_id: Some("modern".to_string()),
            invoice_date: NaiveDate::from_ymd_opt(2024, 1, 15).unwrap(),
            due_date: Some(NaiveDate::from_ymd_opt(2024, 2, 15).unwrap()),
            currency: "USD".to_string(),
            tax: Some(8.5),
            gst: Some(10.0),
            shipping_fee: Some(25.0),
            discount: Some(Discount {
                percentage: Some(10.0),
                price: None,
            }),
            billed_to: Contact {
                name: "Acme Corp".to_string(),
                email: Some("<EMAIL>".to_string()),
                phone: Some("******-0123".to_string()),
                abn: Some("***********".to_string()),
                address: Some(Address {
                    street: "123 Business Ave".to_string(),
                    city: "San Francisco".to_string(),
                    state: "CA".to_string(),
                    post_code: "94102".to_string(),
                    country: "USA".to_string(),
                }),
            },
            from: Contact {
                name: "My Company".to_string(),
                email: Some("<EMAIL>".to_string()),
                phone: Some("******-0456".to_string()),
                abn: Some("***********".to_string()),
                address: Some(Address {
                    street: "456 Vendor St".to_string(),
                    city: "Los Angeles".to_string(),
                    state: "CA".to_string(),
                    post_code: "90210".to_string(),
                    country: "USA".to_string(),
                }),
            },
            items: vec![
                InvoiceItem {
                    name: "Web Development".to_string(),
                    description: Some("Custom website development".to_string()),
                    quantity: 1,
                    price: 2500.0,
                },
                InvoiceItem {
                    name: "Hosting Setup".to_string(),
                    description: None,
                    quantity: 12,
                    price: 50.0,
                },
            ],
        };

        let json = serde_json::to_string_pretty(&request).expect("Failed to serialize request");
        
        // Verify camelCase field names in JSON
        assert!(json.contains("templateId"));
        assert!(json.contains("invoiceDate"));
        assert!(json.contains("dueDate"));
        assert!(json.contains("shippingFee"));
        assert!(json.contains("billedTo"));
        assert!(json.contains("postCode"));
        
        // Verify the JSON can be deserialized back
        let deserialized: InvoiceRequest = serde_json::from_str(&json)
            .expect("Failed to deserialize request");
        
        assert_eq!(deserialized.template_id, request.template_id);
        assert_eq!(deserialized.currency, request.currency);
        assert_eq!(deserialized.items.len(), request.items.len());
    }

    #[test]
    fn test_response_serialization() {
        let now = Utc::now();
        let expires_at = now + chrono::Duration::hours(24);

        let response = InvoiceResponse::success(InvoiceData {
            invoice_id: "inv_123456".to_string(),
            download_url: "https://example.com/download/inv_123456.pdf".to_string(),
            expires_at,
            file_size: 245760,
            created_at: now,
        });

        let json = serde_json::to_string_pretty(&response).expect("Failed to serialize response");
        
        // Verify camelCase field names in JSON
        assert!(json.contains("invoiceId"));
        assert!(json.contains("downloadUrl"));
        assert!(json.contains("expiresAt"));
        assert!(json.contains("fileSize"));
        assert!(json.contains("createdAt"));
        
        // Verify structure
        assert!(json.contains("\"success\": true"));
        assert!(json.contains("\"data\":"));
        assert!(json.contains("\"error\": null"));
    }

    #[test]
    fn test_error_response_serialization() {
        let response = InvoiceResponse::error(ErrorData::validation("Invalid email format"));

        let json = serde_json::to_string_pretty(&response).expect("Failed to serialize error response");
        
        // Verify structure
        assert!(json.contains("\"success\": false"));
        assert!(json.contains("\"data\": null"));
        assert!(json.contains("\"error\":"));
        assert!(json.contains("\"code\": \"VALIDATION_ERROR\""));
        assert!(json.contains("\"message\": \"Invalid email format\""));
    }

    #[test]
    fn test_health_check_serialization() {
        let health = HealthCheckResponse::healthy(
            "invoice-generator-api",
            "0.1.0"
        );

        let json = serde_json::to_string_pretty(&health).expect("Failed to serialize health response");
        
        // Verify camelCase field names and structure
        assert!(json.contains("\"status\": \"healthy\""));
        assert!(json.contains("\"service\": \"invoice-generator-api\""));
        assert!(json.contains("\"version\": \"0.1.0\""));
        assert!(json.contains("\"database\": \"healthy\""));
        assert!(json.contains("\"storage\": \"healthy\""));
    }
} 