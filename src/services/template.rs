// Template rendering service - placeholder
// Will be implemented in Task 7 

use handlebars::{Con<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Help<PERSON>, <PERSON>erR<PERSON>ult, Output, RenderContext, RenderError};
use serde_json::{json, Value};
use chrono::NaiveDate;
use std::time::Instant;

use crate::models::request::InvoiceRequest;
use crate::middleware::monitoring::BusinessMetrics;

#[derive(Debug, Clone)]
pub struct TemplateService {
    handlebars: Handlebars<'static>,
}

impl TemplateService {
    /// Create a new TemplateService with all templates and helpers registered
    pub fn new() -> Result<Self, TemplateError> {
        let mut handlebars = Handlebars::new();
        
        // Register custom helpers
        handlebars.register_helper("format_date", Box::new(format_date_helper));
        handlebars.register_helper("format_currency", Box::new(format_currency_helper));
        handlebars.register_helper("multiply", Box::new(multiply_helper));
        handlebars.register_helper("now", Box::new(now_helper));
        handlebars.register_helper("add", Box::new(add_helper));
        handlebars.register_helper("subtract", Box::new(subtract_helper));
        handlebars.register_helper("percentage", Box::new(percentage_helper));
        handlebars.register_helper("round", Box::new(round_helper));
        
        // Register the default template
        let template_content = include_str!("../templates/default.html");
        handlebars
            .register_template_string("default", template_content)
            .map_err(|e| TemplateError::RegistrationError(e.to_string()))?;
        
        Ok(Self { handlebars })
    }
    
    /// Render an invoice using the specified template
    pub fn render_invoice(&self, request: &InvoiceRequest) -> Result<String, TemplateError> {
        let start_time = Instant::now();
        let template_id = request.template_id.as_deref().unwrap_or("default");

        // Prepare template data
        let template_data = self.prepare_template_data(request)?;

        // Render the template
        let result = self.handlebars
            .render(template_id, &template_data)
            .map_err(|e| TemplateError::RenderError(e.to_string()));

        // Record metrics
        let duration = start_time.elapsed();
        let success = result.is_ok();
        BusinessMetrics::record_template_rendering(template_id, duration, success);

        result
    }
    
    /// Register a new template
    pub fn register_template(&mut self, name: &str, content: &str) -> Result<(), TemplateError> {
        self.handlebars
            .register_template_string(name, content)
            .map_err(|e| TemplateError::RegistrationError(e.to_string()))
    }
    
    /// Get list of available templates
    pub fn list_templates(&self) -> Vec<String> {
        self.handlebars.get_templates().keys().cloned().collect()
    }
    
    /// Prepare template data from InvoiceRequest
    fn prepare_template_data(&self, request: &InvoiceRequest) -> Result<Value, TemplateError> {
        // Calculate financial totals
        let subtotal = self.calculate_subtotal(&request.items)?;
        let discount_amount = self.calculate_discount_amount(subtotal, &request.discount)?;
        let tax_amount = self.calculate_tax_amount(subtotal - discount_amount, request.tax)?;
        let gst_amount = self.calculate_gst_amount(subtotal - discount_amount, request.gst)?;
        let shipping_fee = request.shipping_fee.unwrap_or(0.0);
        let total = subtotal - discount_amount + tax_amount + gst_amount + shipping_fee;
        
        // Create template data structure
        let data = json!({
            "template_id": request.template_id,
            "invoice_date": request.invoice_date.format("%Y-%m-%d").to_string(),
            "due_date": request.due_date.map(|d| d.format("%Y-%m-%d").to_string()),
            "currency": request.currency,
            "billed_to": request.billed_to,
            "from": request.from,
            "items": self.prepare_items_data(&request.items)?,
            "subtotal": subtotal,
            "discount": request.discount,
            "discount_amount": discount_amount,
            "tax": request.tax,
            "tax_amount": tax_amount,
            "gst": request.gst,
            "gst_amount": gst_amount,
            "shipping_fee": shipping_fee,
            "total": total
        });
        
        Ok(data)
    }
    
    /// Prepare items data with calculated totals
    fn prepare_items_data(&self, items: &[crate::models::request::InvoiceItem]) -> Result<Vec<Value>, TemplateError> {
        let mut prepared_items = Vec::new();
        
        for item in items {
            let item_total = (item.quantity as f64) * item.price;
            
            let item_data = json!({
                "name": item.name,
                "description": item.description,
                "quantity": item.quantity,
                "price": item.price,
                "total": item_total
            });
            
            prepared_items.push(item_data);
        }
        
        Ok(prepared_items)
    }
    
    /// Calculate subtotal from all items
    fn calculate_subtotal(&self, items: &[crate::models::request::InvoiceItem]) -> Result<f64, TemplateError> {
        let mut subtotal = 0.0;
        
        for item in items {
            let item_total = (item.quantity as f64) * item.price;
            subtotal += item_total;
        }
        
        Ok(subtotal)
    }
    
    /// Calculate discount amount from discount configuration
    fn calculate_discount_amount(&self, subtotal: f64, discount: &Option<crate::models::request::Discount>) -> Result<f64, TemplateError> {
        if let Some(d) = discount {
            if let Some(percentage) = d.percentage {
                if percentage >= 0.0 && percentage <= 100.0 {
                    Ok(subtotal * percentage / 100.0)
                } else {
                    Err(TemplateError::InvalidData("Discount percentage must be between 0 and 100".to_string()))
                }
            } else if let Some(price) = d.price {
                if price >= 0.0 {
                    Ok(price)
                } else {
                    Err(TemplateError::InvalidData("Discount amount must be non-negative".to_string()))
                }
            } else {
                Ok(0.0)
            }
        } else {
            Ok(0.0)
        }
    }
    
    /// Calculate tax amount from tax rate
    fn calculate_tax_amount(&self, taxable_amount: f64, tax_rate: Option<f64>) -> Result<f64, TemplateError> {
        if let Some(rate) = tax_rate {
            if rate >= 0.0 && rate <= 100.0 {
                Ok(taxable_amount * rate / 100.0)
            } else {
                Err(TemplateError::InvalidData("Tax rate must be between 0 and 100".to_string()))
            }
        } else {
            Ok(0.0)
        }
    }
    
    /// Calculate GST amount from GST rate
    fn calculate_gst_amount(&self, taxable_amount: f64, gst_rate: Option<f64>) -> Result<f64, TemplateError> {
        if let Some(rate) = gst_rate {
            if rate >= 0.0 && rate <= 100.0 {
                Ok(taxable_amount * rate / 100.0)
            } else {
                Err(TemplateError::InvalidData("GST rate must be between 0 and 100".to_string()))
            }
        } else {
            Ok(0.0)
        }
    }

    /// Render a template by name and context (serde_json::Value)
    pub fn render_template(&self, template_name: &str, context: &serde_json::Value) -> Result<String, TemplateError> {
        self.handlebars
            .render(template_name, context)
            .map_err(|e| TemplateError::RenderError(e.to_string()))
    }
}

impl Default for TemplateService {
    fn default() -> Self {
        Self::new().expect("Failed to create default TemplateService")
    }
}

/// Custom error type for template operations
#[derive(Debug, thiserror::Error)]
pub enum TemplateError {
    #[error("Template registration error: {0}")]
    RegistrationError(String),
    
    #[error("Template render error: {0}")]
    RenderError(String),
    
    #[error("Invalid data: {0}")]
    InvalidData(String),
    
    #[error("Template not found: {0}")]
    TemplateNotFound(String),
}

// Handlebars Helper Functions

/// Now helper - returns current date/time
fn now_helper(
    _h: &Helper,
    _: &Handlebars,
    _: &Context,
    _: &mut RenderContext,
    out: &mut dyn Output,
) -> HelperResult {
    let now = chrono::Utc::now();
    let formatted = now.format("%Y-%m-%d").to_string();
    out.write(&formatted)?;
    Ok(())
}

/// Format date helper - formats dates in various formats
fn format_date_helper(
    h: &Helper,
    _: &Handlebars,
    _: &Context,
    _: &mut RenderContext,
    out: &mut dyn Output,
) -> HelperResult {
    let date_str = h
        .param(0)
        .and_then(|v| v.value().as_str())
        .ok_or_else(|| RenderError::new("Date parameter is required"))?;
    
    let format = h
        .param(1)
        .and_then(|v| v.value().as_str())
        .unwrap_or("%B %d, %Y"); // Default format: "January 01, 2024"
    
    // Parse the date string (assuming ISO format YYYY-MM-DD)
    match NaiveDate::parse_from_str(date_str, "%Y-%m-%d") {
        Ok(date) => {
            let formatted = date.format(format).to_string();
            out.write(&formatted)?;
        }
        Err(_) => {
            out.write(date_str)?; // Fallback to original string
        }
    }
    
    Ok(())
}

/// Format currency helper - formats numbers as currency
fn format_currency_helper(
    h: &Helper,
    _: &Handlebars,
    _: &Context,
    _: &mut RenderContext,
    out: &mut dyn Output,
) -> HelperResult {
    let currency = h
        .param(0)
        .and_then(|v| v.value().as_str())
        .unwrap_or("USD");
    
    let amount = h
        .param(1)
        .and_then(|v| v.value().as_f64())
        .ok_or_else(|| RenderError::new("Amount parameter is required"))?;
    
    let symbol = match currency {
        "USD" => "$",
        "EUR" => "€",
        "GBP" => "£",
        "JPY" => "¥",
        "AUD" => "A$",
        "CAD" => "C$",
        _ => currency, // Fallback to currency code
    };
    
    let formatted = format!("{}{:.2}", symbol, amount);
    out.write(&formatted)?;
    
    Ok(())
}

/// Multiply helper - multiplies two numbers
fn multiply_helper(
    h: &Helper,
    _: &Handlebars,
    _: &Context,
    _: &mut RenderContext,
    out: &mut dyn Output,
) -> HelperResult {
    let num1 = h
        .param(0)
        .and_then(|v| v.value().as_f64())
        .ok_or_else(|| RenderError::new("First number parameter is required"))?;
    
    let num2 = h
        .param(1)
        .and_then(|v| v.value().as_f64())
        .ok_or_else(|| RenderError::new("Second number parameter is required"))?;
    
    let result = num1 * num2;
    out.write(&format!("{:.2}", result))?;
    
    Ok(())
}

/// Add helper - adds two numbers
fn add_helper(
    h: &Helper,
    _: &Handlebars,
    _: &Context,
    _: &mut RenderContext,
    out: &mut dyn Output,
) -> HelperResult {
    let a = h
        .param(0)
        .and_then(|v| {
            if let Some(num) = v.value().as_f64() {
                Some(num)
            } else if let Some(str_val) = v.value().as_str() {
                str_val.parse::<f64>().ok()
            } else {
                None
            }
        })
        .ok_or_else(|| RenderError::new("First parameter is required"))?;
    
    let b = h
        .param(1)
        .and_then(|v| {
            if let Some(num) = v.value().as_f64() {
                Some(num)
            } else if let Some(str_val) = v.value().as_str() {
                str_val.parse::<f64>().ok()
            } else {
                None
            }
        })
        .ok_or_else(|| RenderError::new("Second parameter is required"))?;
    
    let result = a + b;
    out.write(&format!("{:.2}", result))?;
    Ok(())
}

/// Subtract helper - subtracts second number from first
fn subtract_helper(
    h: &Helper,
    _: &Handlebars,
    _: &Context,
    _: &mut RenderContext,
    out: &mut dyn Output,
) -> HelperResult {
    let a = h
        .param(0)
        .and_then(|v| {
            if let Some(num) = v.value().as_f64() {
                Some(num)
            } else if let Some(str_val) = v.value().as_str() {
                str_val.parse::<f64>().ok()
            } else {
                None
            }
        })
        .ok_or_else(|| RenderError::new("First parameter is required"))?;
    
    let b = h
        .param(1)
        .and_then(|v| {
            if let Some(num) = v.value().as_f64() {
                Some(num)
            } else if let Some(str_val) = v.value().as_str() {
                str_val.parse::<f64>().ok()
            } else {
                None
            }
        })
        .ok_or_else(|| RenderError::new("Second parameter is required"))?;
    
    let result = a - b;
    out.write(&format!("{:.2}", result))?;
    Ok(())
}

/// Percentage helper - calculates percentage of a number
fn percentage_helper(
    h: &Helper,
    _: &Handlebars,
    _: &Context,
    _: &mut RenderContext,
    out: &mut dyn Output,
) -> HelperResult {
    let amount = h
        .param(0)
        .and_then(|v| {
            if let Some(num) = v.value().as_f64() {
                Some(num)
            } else if let Some(str_val) = v.value().as_str() {
                str_val.parse::<f64>().ok()
            } else {
                None
            }
        })
        .ok_or_else(|| RenderError::new("Amount parameter is required"))?;
    
    let percentage = h
        .param(1)
        .and_then(|v| {
            if let Some(num) = v.value().as_f64() {
                Some(num)
            } else if let Some(str_val) = v.value().as_str() {
                str_val.parse::<f64>().ok()
            } else {
                None
            }
        })
        .ok_or_else(|| RenderError::new("Percentage parameter is required"))?;
    
    let result = amount * percentage / 100.0;
    out.write(&format!("{:.2}", result))?;
    Ok(())
}

/// Round helper - rounds a number to specified decimal places
fn round_helper(
    h: &Helper,
    _: &Handlebars,
    _: &Context,
    _: &mut RenderContext,
    out: &mut dyn Output,
) -> HelperResult {
    let number = h
        .param(0)
        .and_then(|v| {
            if let Some(num) = v.value().as_f64() {
                Some(num)
            } else if let Some(str_val) = v.value().as_str() {
                str_val.parse::<f64>().ok()
            } else {
                None
            }
        })
        .ok_or_else(|| RenderError::new("Number parameter is required"))?;
    
    let places = h
        .param(1)
        .and_then(|v| v.value().as_u64())
        .unwrap_or(2) as u32;
    
    let multiplier = 10_f64.powi(places as i32);
    let result = (number * multiplier).round() / multiplier;
    out.write(&format!("{:.prec$}", result, prec = places as usize))?;
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::models::request::*;
    use chrono::NaiveDate;
    // Test imports removed - using direct values instead
    
    fn create_test_invoice_request() -> InvoiceRequest {
        InvoiceRequest {
            template_id: Some("default".to_string()),
            invoice_date: NaiveDate::from_ymd_opt(2024, 1, 15).unwrap(),
            due_date: Some(NaiveDate::from_ymd_opt(2024, 2, 15).unwrap()),
            currency: "USD".to_string(),
            tax: Some(10.0),
            gst: Some(5.0),
            shipping_fee: Some(25.0),
            discount: Some(Discount {
                percentage: Some(10.0),
                price: None,
            }),
            billed_to: Contact {
                name: "John Doe".to_string(),
                email: Some("<EMAIL>".to_string()),
                phone: Some("+*********0".to_string()),
                abn: Some("*********".to_string()),
                address: Some(Address {
                    street: "123 Main St".to_string(),
                    state: "CA".to_string(),
                    city: "San Francisco".to_string(),
                    post_code: "94102".to_string(),
                    country: "USA".to_string(),
                }),
            },
            from: Contact {
                name: "ACME Corp".to_string(),
                email: Some("<EMAIL>".to_string()),
                phone: Some("+1*********".to_string()),
                abn: Some("*********".to_string()),
                address: Some(Address {
                    street: "456 Business Ave".to_string(),
                    state: "NY".to_string(),
                    city: "New York".to_string(),
                    post_code: "10001".to_string(),
                    country: "USA".to_string(),
                }),
            },
            items: vec![
                InvoiceItem {
                    name: "Web Development".to_string(),
                    description: Some("Frontend development services".to_string()),
                    quantity: 40,
                    price: 100.0,
                },
                InvoiceItem {
                    name: "Hosting".to_string(),
                    description: Some("Monthly hosting fee".to_string()),
                    quantity: 1,
                    price: 50.0,
                },
            ],
        }
    }
    
    #[test]
    fn test_template_service_creation() {
        let service = TemplateService::new();
        assert!(service.is_ok());
        
        let service = service.unwrap();
        let templates = service.list_templates();
        assert!(templates.contains(&"default".to_string()));
    }
    
    #[test]
    fn test_calculate_subtotal() {
        let service = TemplateService::new().unwrap();
        let request = create_test_invoice_request();
        
        let subtotal = service.calculate_subtotal(&request.items).unwrap();
        assert_eq!(subtotal, 4050.0); // (40 * 100.0) + (1 * 50.0)
    }
    
    #[test]
    fn test_calculate_discount_amount() {
        let service = TemplateService::new().unwrap();
        let subtotal = 1000.0;
        
        // Test percentage discount
        let discount = Some(Discount {
            percentage: Some(10.0),
            price: None,
        });
        let discount_amount = service.calculate_discount_amount(subtotal, &discount).unwrap();
        assert_eq!(discount_amount, 100.0);
        
        // Test fixed price discount
        let discount = Some(Discount {
            percentage: None,
            price: Some(50.0),
        });
        let discount_amount = service.calculate_discount_amount(subtotal, &discount).unwrap();
        assert_eq!(discount_amount, 50.0);
    }
    
    #[test]
    fn test_calculate_tax_amount() {
        let service = TemplateService::new().unwrap();
        let taxable_amount = 1000.0;
        
        let tax_amount = service.calculate_tax_amount(taxable_amount, Some(10.0)).unwrap();
        assert_eq!(tax_amount, 100.0);
        
        let tax_amount = service.calculate_tax_amount(taxable_amount, None).unwrap();
        assert_eq!(tax_amount, 0.0);
    }
    
    #[test]
    fn test_render_invoice() {
        let service = TemplateService::new().unwrap();
        let request = create_test_invoice_request();
        
        let result = service.render_invoice(&request);
        if let Err(ref e) = result {
            println!("Error: {:?}", e);
        }
        assert!(result.is_ok());
        
        let html = result.unwrap();
        assert!(html.contains("John Doe")); // Billed to name
        assert!(html.contains("ACME Corp")); // From name
        assert!(html.contains("Web Development")); // Item name

        
        assert!(html.contains("$")); // Currency symbol
    }
    
    #[test]
    fn test_prepare_template_data() {
        let service = TemplateService::new().unwrap();
        let request = create_test_invoice_request();
        
        let data = service.prepare_template_data(&request).unwrap();
        
        // Verify basic fields
        assert_eq!(data["currency"], "USD");
        assert_eq!(data["invoice_date"], "2024-01-15");
        assert_eq!(data["due_date"], "2024-02-15");
        
        // Verify calculated amounts
        assert_eq!(data["subtotal"], 4050.0);
        assert_eq!(data["discount_amount"], 405.0); // 10% of 4050
        assert_eq!(data["tax_amount"], 364.5); // 10% of (4050 - 405)
        assert_eq!(data["gst_amount"], 182.25); // 5% of (4050 - 405)
        assert_eq!(data["shipping_fee"], 25.0);
        assert_eq!(data["total"], 4216.75); // 4050 - 405 + 364.5 + 182.25 + 25
    }
} 