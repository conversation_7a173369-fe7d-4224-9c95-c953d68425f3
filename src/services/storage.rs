// Storage service for R2 integration - placeholder
// Will be implemented in Task 3 

use aws_config::{BehaviorVersion, Region};
use aws_sdk_s3::{
    config::Credentials,
    error::SdkError,
    presigning::PresigningConfig,
    primitives::ByteStream,
    Client as S3Client,
};
use chrono::{DateTime, Duration, Utc};
use std::time::{Duration as StdDuration, Instant};
use thiserror::Error;
use tracing::{error, info, warn};
use uuid::Uuid;

use crate::config::Config;
use crate::middleware::monitoring::BusinessMetrics;

#[derive(Error, Debug)]
pub enum StorageError {
    #[error("Failed to initialize R2 client: {0}")]
    ClientInitialization(String),
    #[error("Failed to upload file: {0}")]
    Upload(String),
    #[error("Failed to generate pre-signed URL: {0}")]
    PresignedUrl(String),
    #[error("Failed to delete file: {0}")]
    Delete(String),
    #[error("Invalid configuration: {0}")]
    Configuration(String),
}

pub type StorageResult<T> = Result<T, StorageError>;

#[derive(Clone)]
pub struct StorageService {
    client: S3Client,
    bucket_name: String,
    presigned_url_expiry: StdDuration,
}

impl StorageService {
    /// Initialize a new StorageService with R2 configuration
    pub async fn new(config: &Config) -> StorageResult<Self> {
        let client = Self::initialize_r2_client(config).await?;
        
        Ok(StorageService {
            client,
            bucket_name: config.r2_bucket_name.clone(),
            presigned_url_expiry: StdDuration::from_secs(7 * 24 * 60 * 60), // 7 days
        })
    }

    /// Initialize the R2 client with proper credentials and configuration
    async fn initialize_r2_client(config: &Config) -> StorageResult<S3Client> {
        // Validate configuration
        if config.r2_access_key.is_empty() || config.r2_secret_key.is_empty() {
            return Err(StorageError::Configuration(
                "R2 access key and secret key must be provided".to_string(),
            ));
        }

        if config.r2_bucket_name.is_empty() {
            return Err(StorageError::Configuration(
                "R2 bucket name must be provided".to_string(),
            ));
        }

        // Create credentials
        let credentials = Credentials::new(
            &config.r2_access_key,
            &config.r2_secret_key,
            None, // session token
            None, // expiration
            "r2-credentials",
        );

        // Create AWS config with R2 endpoint
        let aws_config = aws_config::defaults(BehaviorVersion::latest())
            .region(Region::new(config.r2_region.clone()))
            .credentials_provider(credentials)
            .endpoint_url(&config.r2_endpoint)
            .load()
            .await;

        // Create S3 client
        let client = S3Client::new(&aws_config);

        info!("R2 client initialized successfully");
        Ok(client)
    }

    /// Upload a PDF file to R2 storage
    pub async fn upload_pdf(&self, file_data: Vec<u8>, key: String) -> StorageResult<String> {
        let start_time = Instant::now();
        let content_length = file_data.len() as i64;

        info!("Uploading PDF to R2: key={}, size={} bytes", key, content_length);

        let byte_stream = ByteStream::from(file_data);

        let result = self
            .client
            .put_object()
            .bucket(&self.bucket_name)
            .key(&key)
            .body(byte_stream)
            .content_type("application/pdf")
            .content_length(content_length)
            .send()
            .await;

        // Record metrics
        let duration = start_time.elapsed();
        let success = result.is_ok();
        let file_size = if success { Some(content_length as usize) } else { None };
        BusinessMetrics::record_storage_operation("upload", duration, success, file_size);

        match result {
            Ok(_) => {
                info!("Successfully uploaded PDF to R2: {}", key);
                Ok(key)
            }
            Err(SdkError::ServiceError(service_error)) => {
                error!("R2 service error during upload: {:?}", service_error);
                Err(StorageError::Upload(format!("{:?}", service_error)))
            }
            Err(other_error) => {
                error!("R2 upload error: {:?}", other_error);
                Err(StorageError::Upload(format!("Upload failed: {}", other_error)))
            }
        }
    }

    /// Generate a pre-signed URL for downloading a PDF
    pub async fn generate_presigned_url(&self, key: &str) -> StorageResult<String> {
        info!("Generating pre-signed URL for key: {}", key);

        let presigning_config = PresigningConfig::expires_in(self.presigned_url_expiry)
            .map_err(|e| {
                error!("Failed to create presigning config: {:?}", e);
                StorageError::PresignedUrl(format!("Invalid expiry duration: {}", e))
            })?;

        let presigned_request = self
            .client
            .get_object()
            .bucket(&self.bucket_name)
            .key(key)
            .presigned(presigning_config)
            .await
            .map_err(|e| {
                error!("Failed to generate pre-signed URL: {:?}", e);
                StorageError::PresignedUrl(format!("Presigning failed: {}", e))
            })?;

        let url = presigned_request.uri().to_string();
        info!("Generated pre-signed URL for key: {}", key);
        Ok(url)
    }

    /// Delete a specific file from R2 storage
    pub async fn delete_file(&self, key: &str) -> StorageResult<()> {
        info!("Deleting file from R2: {}", key);

        let result = self
            .client
            .delete_object()
            .bucket(&self.bucket_name)
            .key(key)
            .send()
            .await;

        match result {
            Ok(_) => {
                info!("Successfully deleted file from R2: {}", key);
                Ok(())
            }
            Err(SdkError::ServiceError(service_error)) => {
                error!("R2 service error during deletion: {:?}", service_error);
                Err(StorageError::Delete(format!("{:?}", service_error)))
            }
            Err(other_error) => {
                error!("R2 deletion error: {:?}", other_error);
                Err(StorageError::Delete(format!("Deletion failed: {}", other_error)))
            }
        }
    }

    /// Delete expired files from R2 storage (files older than retention period)
    pub async fn delete_expired_files(&self, retention_days: i64) -> StorageResult<Vec<String>> {
        info!("Starting cleanup of expired files (older than {} days)", retention_days);

        let cutoff_date = Utc::now() - Duration::days(retention_days);
        let mut deleted_files = Vec::new();

        // List objects in the bucket
        let list_result = self
            .client
            .list_objects_v2()
            .bucket(&self.bucket_name)
            .send()
            .await
            .map_err(|e| {
                error!("Failed to list objects in R2 bucket: {:?}", e);
                StorageError::Delete(format!("Failed to list objects: {}", e))
            })?;

        let objects = list_result.contents();
        for object in objects {
            if let (Some(key), Some(last_modified)) = (object.key(), object.last_modified()) {
                // Convert AWS DateTime to chrono DateTime
                let last_modified_chrono = DateTime::from_timestamp(
                    last_modified.secs(),
                    last_modified.subsec_nanos(),
                );

                if let Some(last_modified_dt) = last_modified_chrono {
                    if last_modified_dt < cutoff_date {
                        match self.delete_file(key).await {
                            Ok(()) => {
                                deleted_files.push(key.to_string());
                                info!("Deleted expired file: {}", key);
                            }
                            Err(e) => {
                                warn!("Failed to delete expired file {}: {}", key, e);
                            }
                        }
                    }
                }
            }
        }

        info!("Cleanup completed. Deleted {} expired files", deleted_files.len());
        Ok(deleted_files)
    }

    /// Generate a unique file key for storing a PDF
    pub fn generate_file_key(&self, invoice_id: &Uuid) -> String {
        let timestamp = Utc::now().format("%Y/%m/%d");
        format!("invoices/{}/{}.pdf", timestamp, invoice_id)
    }

    /// Generate a unique file key with custom prefix
    pub fn generate_file_key_with_prefix(&self, prefix: &str, invoice_id: &Uuid) -> String {
        let timestamp = Utc::now().format("%Y/%m/%d");
        format!("{}/{}/{}.pdf", prefix, timestamp, invoice_id)
    }

    /// Check if the R2 client is properly configured by testing bucket access
    pub async fn health_check(&self) -> StorageResult<()> {
        info!("Performing R2 health check");

        let result = self
            .client
            .head_bucket()
            .bucket(&self.bucket_name)
            .send()
            .await;

        match result {
            Ok(_) => {
                info!("R2 health check passed");
                Ok(())
            }
            Err(e) => {
                error!("R2 health check failed: {:?}", e);
                Err(StorageError::Configuration(format!(
                    "R2 bucket not accessible: {}",
                    e
                )))
            }
        }
    }

    /// Get bucket name (useful for debugging and logging)
    pub fn bucket_name(&self) -> &str {
        &self.bucket_name
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    fn create_test_config() -> Config {
        Config {
            port: 3000,
            database_url: "test".to_string(),
            r2_endpoint: "https://test.r2.cloudflarestorage.com".to_string(),
            r2_access_key: "test_access_key".to_string(),
            r2_secret_key: "test_secret_key".to_string(),
            r2_bucket_name: "test-bucket".to_string(),
            r2_region: "auto".to_string(),
            rate_limit: crate::config::RateLimitConfig {
                max_requests: 5,
                window_duration_minutes: 1,
            },
            pdf_retention_days: 7,
            max_request_size: 1048576,
            wkhtmltopdf_path: None,
            database_max_connections: 100,
            database_min_connections: 5,
            database_connect_timeout: 8,
            security: crate::config::SecurityConfig {
                enable_hsts: true,
                enable_csp: true,
                request_timeout_seconds: 30,
                allowed_origins: vec!["*".to_string()],
                enable_security_headers: true,
            },
            monitoring: crate::config::MonitoringConfig {
                enable_metrics: true,
                enable_tracing: true,
                log_level: "info".to_string(),
                enable_correlation_ids: true,
                enable_request_logging: true,
                metrics_endpoint_enabled: true,
            },
        }
    }

    #[tokio::test]
    async fn test_generate_file_key() {
        let config = create_test_config();
        let aws_config = aws_config::defaults(BehaviorVersion::latest()).load().await;
        let storage = StorageService {
            client: S3Client::new(&aws_config),
            bucket_name: config.r2_bucket_name.clone(),
            presigned_url_expiry: StdDuration::from_secs(3600),
        };

        let invoice_id = Uuid::new_v4();
        let key = storage.generate_file_key(&invoice_id);
        
        assert!(key.starts_with("invoices/"));
        assert!(key.ends_with(&format!("{}.pdf", invoice_id)));
    }

    #[tokio::test]
    async fn test_generate_file_key_with_prefix() {
        let config = create_test_config();
        let aws_config = aws_config::defaults(BehaviorVersion::latest()).load().await;
        let storage = StorageService {
            client: S3Client::new(&aws_config),
            bucket_name: config.r2_bucket_name.clone(),
            presigned_url_expiry: StdDuration::from_secs(3600),
        };

        let invoice_id = Uuid::new_v4();
        let key = storage.generate_file_key_with_prefix("test-prefix", &invoice_id);
        
        assert!(key.starts_with("test-prefix/"));
        assert!(key.ends_with(&format!("{}.pdf", invoice_id)));
    }

    #[tokio::test]
    async fn test_bucket_name() {
        let config = create_test_config();
        let aws_config = aws_config::defaults(BehaviorVersion::latest()).load().await;
        let storage = StorageService {
            client: S3Client::new(&aws_config),
            bucket_name: config.r2_bucket_name.clone(),
            presigned_url_expiry: StdDuration::from_secs(3600),
        };

        assert_eq!(storage.bucket_name(), "test-bucket");
    }
} 