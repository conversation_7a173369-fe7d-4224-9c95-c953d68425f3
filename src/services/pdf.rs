// PDF generation service - placeholder
// Will be implemented in Task 8 

use std::time::Duration;
use std::path::Path;
use thiserror::Error;
use serde::{Serialize, Deserialize};
use headless_chrome::{<PERSON>rowser, LaunchOptions, Tab};
use headless_chrome::types::PrintToPdfOptions;
use std::ffi::OsStr;
use tempfile::TempDir;
use tracing::{warn, error, debug, info};
use std::sync::Arc;
use std::sync::atomic::{AtomicBool, Ordering};
use std::time::Instant;

use crate::middleware::monitoring::BusinessMetrics;

/// PDF generation errors
#[derive(Error, Debug)]
pub enum PdfError {
    #[error("Browser launch failed: {0}")]
    BrowserLaunchFailed(String),
    
    #[error("Page creation failed: {0}")]
    PageCreationFailed(String),
    
    #[error("PDF generation failed: {0}")]
    GenerationFailed(String),
    
    #[error("Timeout occurred during PDF generation")]
    Timeout,
    
    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),
    
    #[error("Template error: {0}")]
    TemplateError(String),
    
    #[error("Invalid configuration: {0}")]
    InvalidConfig(String),
    
    #[error("Resource cleanup failed: {0}")]
    CleanupFailed(String),
}

impl From<crate::services::template::TemplateError> for PdfError {
    fn from(err: crate::services::template::TemplateError) -> Self {
        PdfError::TemplateError(err.to_string())
    }
}

/// PDF page orientation
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum PageOrientation {
    Portrait,
    Landscape,
}

impl Default for PageOrientation {
    fn default() -> Self {
        PageOrientation::Portrait
    }
}

/// Resource tracker for cleanup management
struct ResourceTracker {
    temp_dir: Option<TempDir>,
    browser: Option<Browser>,
    cleanup_completed: Arc<AtomicBool>,
}

impl ResourceTracker {
    fn new() -> Self {
        Self {
            temp_dir: None,
            browser: None,
            cleanup_completed: Arc::new(AtomicBool::new(false)),
        }
    }
    
    fn set_temp_dir(&mut self, temp_dir: TempDir) {
        self.temp_dir = Some(temp_dir);
    }
    
    fn set_browser(&mut self, browser: Browser) {
        self.browser = Some(browser);
    }
    
    /// Perform cleanup of all resources
    async fn cleanup(&mut self) {
        if self.cleanup_completed.load(Ordering::Acquire) {
            return; // Already cleaned up
        }
        
        debug!("Starting resource cleanup for PDF generation");
        
        // Close browser first
        if let Some(browser) = self.browser.take() {
            if let Err(e) = self.cleanup_browser(browser).await {
                warn!("Failed to cleanup browser: {}", e);
            }
        }
        
        // Clean up temporary directory
        if let Some(temp_dir) = self.temp_dir.take() {
            if let Err(e) = self.cleanup_temp_dir(temp_dir) {
                warn!("Failed to cleanup temporary directory: {}", e);
            }
        }
        
        self.cleanup_completed.store(true, Ordering::Release);
        debug!("Resource cleanup completed");
    }
    
    async fn cleanup_browser(&self, browser: Browser) -> Result<(), PdfError> {
        debug!("Closing browser instance");
        
        // Try to gracefully close all tabs first
        // Note: get_tabs() returns a reference to Arc<Mutex<Vec<Arc<Tab>>>>
        if let Ok(tabs_guard) = browser.get_tabs().lock() {
            for tab in tabs_guard.iter() {
                if let Err(e) = tab.close(false) {
                    warn!("Failed to close tab: {}", e);
                }
            }
        }
        
        // Browser will be dropped automatically, but we can force cleanup
        drop(browser);
        
        // Give the browser process a moment to shut down gracefully
        tokio::time::sleep(Duration::from_millis(100)).await;
        
        Ok(())
    }
    
    fn cleanup_temp_dir(&self, temp_dir: TempDir) -> Result<(), PdfError> {
        debug!("Cleaning up temporary directory: {:?}", temp_dir.path());
        
        // TempDir automatically cleans up when dropped, but we can handle errors
        if let Err(e) = temp_dir.close() {
            return Err(PdfError::CleanupFailed(format!(
                "Failed to remove temporary directory: {}", e
            )));
        }
        
        Ok(())
    }
}

impl Drop for ResourceTracker {
    fn drop(&mut self) {
        if !self.cleanup_completed.load(Ordering::Acquire) {
            warn!("ResourceTracker dropped without proper cleanup - forcing cleanup");
            
            // Synchronous cleanup for Drop
            if let Some(browser) = self.browser.take() {
                drop(browser);
            }
            
            if let Some(temp_dir) = self.temp_dir.take() {
                if let Err(e) = temp_dir.close() {
                    error!("Emergency cleanup failed for temp directory: {}", e);
                }
            }
            
            self.cleanup_completed.store(true, Ordering::Release);
        }
    }
}

/// PDF generation configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PdfConfig {
    /// Page size (e.g., "A4", "Letter", "Legal")
    pub page_size: String,
    
    /// Page orientation
    pub orientation: PageOrientation,
    
    /// Top margin in millimeters
    pub margin_top: u32,
    
    /// Right margin in millimeters
    pub margin_right: u32,
    
    /// Bottom margin in millimeters
    pub margin_bottom: u32,
    
    /// Left margin in millimeters
    pub margin_left: u32,
    
    /// Whether to print background graphics and colors
    pub print_background: bool,
    
    /// Scale factor for the page (0.1 to 2.0)
    pub scale: f32,
    
    /// Timeout for PDF generation
    pub timeout: Duration,
    
    /// Compression level (0-9, where 9 is maximum compression)
    pub compression_level: Option<u8>,
    
    /// Image quality (0-100, where 100 is best quality)
    pub image_quality: Option<u8>,
    
    /// Whether to embed fonts in the PDF
    pub embed_fonts: bool,
    
    /// PDF metadata: document title
    pub title: Option<String>,
    
    /// PDF metadata: document author
    pub author: Option<String>,
    
    /// PDF metadata: document subject
    pub subject: Option<String>,
    
    /// PDF metadata: document keywords
    pub keywords: Option<Vec<String>>,
    
    /// PDF metadata: document creator
    pub creator: Option<String>,
}

impl Default for PdfConfig {
    fn default() -> Self {
        Self {
            page_size: "A4".to_string(),
            orientation: PageOrientation::Portrait,
            margin_top: 20,
            margin_right: 20,
            margin_bottom: 20,
            margin_left: 20,
            print_background: true,
            scale: 1.0,
            timeout: Duration::from_secs(30),
            compression_level: Some(6), // Balanced compression
            image_quality: Some(85), // Good quality with reasonable file size
            embed_fonts: true,
            title: None,
            author: None,
            subject: None,
            keywords: None,
            creator: Some("Invoice Generator API".to_string()),
        }
    }
}

impl PdfConfig {
    /// Create a new PDF configuration with default values
    pub fn new() -> Self {
        Self::default()
    }
    
    /// Set the page size
    pub fn with_page_size(mut self, page_size: impl Into<String>) -> Self {
        self.page_size = page_size.into();
        self
    }
    
    /// Set the page orientation
    pub fn with_orientation(mut self, orientation: PageOrientation) -> Self {
        self.orientation = orientation;
        self
    }
    
    /// Set all margins to the same value
    pub fn with_margins(mut self, margin: u32) -> Self {
        self.margin_top = margin;
        self.margin_right = margin;
        self.margin_bottom = margin;
        self.margin_left = margin;
        self
    }
    
    /// Set individual margins
    pub fn with_custom_margins(mut self, top: u32, right: u32, bottom: u32, left: u32) -> Self {
        self.margin_top = top;
        self.margin_right = right;
        self.margin_bottom = bottom;
        self.margin_left = left;
        self
    }
    
    /// Set the scale factor
    pub fn with_scale(mut self, scale: f32) -> Self {
        self.scale = scale.clamp(0.1, 2.0); // Ensure valid range
        self
    }
    
    /// Set the timeout
    pub fn with_timeout(mut self, timeout: Duration) -> Self {
        self.timeout = timeout;
        self
    }
    
    /// Set compression level
    pub fn with_compression(mut self, level: u8) -> Self {
        self.compression_level = Some(level.clamp(0, 9));
        self
    }
    
    /// Set image quality
    pub fn with_image_quality(mut self, quality: u8) -> Self {
        self.image_quality = Some(quality.clamp(0, 100));
        self
    }
    
    /// Set metadata
    pub fn with_metadata(
        mut self,
        title: Option<String>,
        author: Option<String>,
        subject: Option<String>,
        keywords: Option<Vec<String>>,
    ) -> Self {
        self.title = title;
        self.author = author;
        self.subject = subject;
        self.keywords = keywords;
        self
    }
    
    /// Validate the configuration
    pub fn validate(&self) -> Result<(), PdfError> {
        // Validate timeout
        if self.timeout.as_secs() == 0 {
            return Err(PdfError::InvalidConfig("Timeout must be greater than 0".to_string()));
        }
        
        if self.timeout.as_secs() > 300 { // 5 minutes max
            return Err(PdfError::InvalidConfig("Timeout cannot exceed 5 minutes".to_string()));
        }
        
        // Validate scale
        if self.scale < 0.1 || self.scale > 2.0 {
            return Err(PdfError::InvalidConfig("Scale must be between 0.1 and 2.0".to_string()));
        }
        
        // Validate margins (reasonable limits)
        let max_margin = 100; // 100mm
        if self.margin_top > max_margin || self.margin_right > max_margin ||
           self.margin_bottom > max_margin || self.margin_left > max_margin {
            return Err(PdfError::InvalidConfig("Margins cannot exceed 100mm".to_string()));
        }
        
        // Validate compression level
        if let Some(level) = self.compression_level {
            if level > 9 {
                return Err(PdfError::InvalidConfig("Compression level must be between 0-9".to_string()));
            }
        }
        
        // Validate image quality
        if let Some(quality) = self.image_quality {
            if quality > 100 {
                return Err(PdfError::InvalidConfig("Image quality must be between 0-100".to_string()));
            }
        }
        
        Ok(())
    }
}

/// PDF service for generating PDFs from HTML content
#[derive(Clone)]
pub struct PdfService {
    default_config: PdfConfig,
}

impl PdfService {
    /// Create a new PDF service with default configuration
    pub fn new() -> Self {
        Self {
            default_config: PdfConfig::default(),
        }
    }
    
    /// Create a new PDF service with custom default configuration
    pub fn with_config(config: PdfConfig) -> Result<Self, PdfError> {
        config.validate()?;
        Ok(Self {
            default_config: config,
        })
    }
    
    /// Get the default configuration
    pub fn default_config(&self) -> &PdfConfig {
        &self.default_config
    }
    
    /// Set the default configuration
    pub fn set_default_config(&mut self, config: PdfConfig) -> Result<(), PdfError> {
        config.validate()?;
        self.default_config = config;
        Ok(())
    }
    
    /// Generate PDF from HTML content using the default configuration
    pub async fn generate_pdf_from_html(&self, html_content: &str) -> Result<Vec<u8>, PdfError> {
        let start_time = Instant::now();
        let result = self.generate_pdf_from_html_with_config(html_content, &self.default_config).await;

        // Record metrics
        let duration = start_time.elapsed();
        let success = result.is_ok();
        let file_size = result.as_ref().ok().map(|data| data.len());
        BusinessMetrics::record_pdf_generation(duration, success, file_size);

        result
    }
    
    /// Generate PDF from HTML content with custom configuration
    pub async fn generate_pdf_from_html_with_config(
        &self,
        html_content: &str,
        config: &PdfConfig,
    ) -> Result<Vec<u8>, PdfError> {
        // Validate configuration
        config.validate()?;
        
        info!("Starting PDF generation with timeout: {:?}", config.timeout);
        
        // Create resource tracker for cleanup management
        let mut resource_tracker = ResourceTracker::new();
        
        // Wrap the entire PDF generation process with timeout and cleanup
        let pdf_generation = async {
            // Create temporary directory for any intermediate files
            let temp_dir = tempfile::tempdir()
                .map_err(|e| PdfError::IoError(e))?;
            debug!("Created temporary directory: {:?}", temp_dir.path());
            resource_tracker.set_temp_dir(temp_dir);
            
            // Launch browser with optimized options
            let browser = self.launch_browser(config).await?;
            debug!("Browser launched successfully");
            resource_tracker.set_browser(browser);
            
            // Get browser reference for PDF generation
            let browser_ref = resource_tracker.browser.as_ref()
                .ok_or_else(|| PdfError::GenerationFailed("Browser not available".to_string()))?;
            
            // Create a new tab
            let tab = browser_ref.new_tab()
                .map_err(|e| PdfError::PageCreationFailed(format!("Failed to create new tab: {}", e)))?;
            debug!("Browser tab created successfully");
            
            // Set content and generate PDF
            let pdf_data = self.render_html_to_pdf(&tab, html_content, config).await?;
            info!("PDF generation completed successfully, size: {} bytes", pdf_data.len());
            
            Ok::<Vec<u8>, PdfError>(pdf_data)
        };
        
        // Apply timeout to the PDF generation process
        let result = match tokio::time::timeout(config.timeout, pdf_generation).await {
            Ok(result) => result,
            Err(_) => {
                error!("PDF generation timed out after {:?}", config.timeout);
                Err(PdfError::Timeout)
            }
        };
        
        // Always perform cleanup, regardless of success or failure
        resource_tracker.cleanup().await;
        
        result
    }
    
    /// Generate PDF from HTML file using the default configuration
    pub async fn generate_pdf_from_file<P: AsRef<Path>>(&self, html_file_path: P) -> Result<Vec<u8>, PdfError> {
        self.generate_pdf_from_file_with_config(html_file_path, &self.default_config).await
    }
    
    /// Generate PDF from HTML file with custom configuration
    pub async fn generate_pdf_from_file_with_config<P: AsRef<Path>>(
        &self,
        html_file_path: P,
        config: &PdfConfig,
    ) -> Result<Vec<u8>, PdfError> {
        // Validate configuration
        config.validate()?;
        
        // Read HTML file
        let html_content = std::fs::read_to_string(html_file_path)
            .map_err(|e| PdfError::IoError(e))?;
        
        // Generate PDF from content
        self.generate_pdf_from_html_with_config(&html_content, config).await
    }
    
    /// Save PDF to file from HTML content using the default configuration
    pub async fn save_pdf_from_html<P: AsRef<Path>>(
        &self,
        html_content: &str,
        output_path: P,
    ) -> Result<(), PdfError> {
        self.save_pdf_from_html_with_config(html_content, output_path, &self.default_config).await
    }
    
    /// Save PDF to file from HTML content with custom configuration
    pub async fn save_pdf_from_html_with_config<P: AsRef<Path>>(
        &self,
        html_content: &str,
        output_path: P,
        config: &PdfConfig,
    ) -> Result<(), PdfError> {
        let pdf_data = self.generate_pdf_from_html_with_config(html_content, config).await?;
        std::fs::write(output_path, pdf_data)
            .map_err(|e| PdfError::IoError(e))?;
        Ok(())
    }
    
    /// Launch browser with optimized settings for PDF generation
    async fn launch_browser(&self, _config: &PdfConfig) -> Result<Browser, PdfError> {
        debug!("Launching headless Chrome browser for PDF generation");
        
        let launch_options = LaunchOptions::default_builder()
            .headless(true)
            .sandbox(false) // Disable sandbox for better compatibility
            .args(vec![
                OsStr::new("--no-sandbox"),
                OsStr::new("--disable-setuid-sandbox"),
                OsStr::new("--disable-dev-shm-usage"),
                OsStr::new("--disable-gpu"),
                OsStr::new("--disable-background-timer-throttling"),
                OsStr::new("--disable-backgrounding-occluded-windows"),
                OsStr::new("--disable-renderer-backgrounding"),
                OsStr::new("--disable-features=TranslateUI"),
                OsStr::new("--disable-ipc-flooding-protection"),
                OsStr::new("--disable-hang-monitor"),
                OsStr::new("--disable-client-side-phishing-detection"),
                OsStr::new("--disable-popup-blocking"),
                OsStr::new("--disable-default-apps"),
                OsStr::new("--disable-prompt-on-repost"),
                OsStr::new("--disable-domain-reliability"),
                OsStr::new("--disable-component-extensions-with-background-pages"),
                OsStr::new("--disable-extensions"),
                OsStr::new("--disable-plugins"),
                OsStr::new("--disable-images"), // Optimize for text-based invoices
                OsStr::new("--run-all-compositor-stages-before-draw"),
                OsStr::new("--virtual-time-budget=5000"), // 5 second budget
                OsStr::new("--disable-background-networking"),
                OsStr::new("--disable-default-apps"),
                OsStr::new("--disable-sync"),
                OsStr::new("--no-first-run"),
                OsStr::new("--disable-translate"),
            ])
            .build()
            .map_err(|e| PdfError::InvalidConfig(format!("Invalid launch options: {}", e)))?;
        
        Browser::new(launch_options)
            .map_err(|e| PdfError::BrowserLaunchFailed(format!("Failed to launch browser: {}", e)))
    }
    
    /// Render HTML content to PDF using the specified tab and configuration
    async fn render_html_to_pdf(
        &self,
        tab: &Tab,
        html_content: &str,
        config: &PdfConfig,
    ) -> Result<Vec<u8>, PdfError> {
        debug!("Starting HTML to PDF rendering");
        
        // Apply optimization settings to HTML content
        let optimized_html = self.optimize_html_content(html_content, config);
        
        // Use evaluate to set document content
        let script = format!(
            "document.open(); document.write({}); document.close();",
            serde_json::to_string(&optimized_html)
                .map_err(|e| PdfError::PageCreationFailed(format!("Failed to serialize HTML: {}", e)))?
        );
        tab.evaluate(&script, false)
            .map_err(|e| PdfError::PageCreationFailed(format!("Failed to set HTML content: {}", e)))?;
        
        debug!("Optimized HTML content set successfully");
        
        // Wait for page to load and render with timeout protection
        // We need to use a different approach since wait_until_navigated requires sync context
        let navigation_start = std::time::Instant::now();
        let max_navigation_time = Duration::from_secs(10);
        
        // Try to wait for navigation completion
        let mut navigation_successful = false;
        while navigation_start.elapsed() < max_navigation_time {
            // Check if we can proceed (this is a simplified check)
            // In a real implementation, you might want to check page load state via evaluate
            tokio::time::sleep(Duration::from_millis(100)).await;
            
            // For now, we'll assume navigation is complete after a short delay
            if navigation_start.elapsed() > Duration::from_millis(500) {
                navigation_successful = true;
                break;
            }
        }
        
        if navigation_successful {
            debug!("Page navigation completed successfully");
        } else {
            warn!("Navigation timeout - proceeding with PDF generation anyway");
        }
        
        // Wait for any fonts and stylesheets to load with configurable delay
        let render_delay = if config.embed_fonts {
            Duration::from_millis(3000) // Extra time for font embedding
        } else {
            Duration::from_millis(1500) // Shorter delay when not embedding fonts
        };
        debug!("Waiting {:?} for fonts and styles to load (embed_fonts={})", render_delay, config.embed_fonts);
        tokio::time::sleep(render_delay).await;
        
        // Configure PDF options
        let pdf_options = self.build_pdf_options(config)?;
        debug!("PDF options configured");
        
        // Generate PDF with additional error context
        let pdf_data = tab.print_to_pdf(Some(pdf_options))
            .map_err(|e| PdfError::GenerationFailed(format!("Failed to generate PDF: {}", e)))?;
        
        debug!("PDF generated successfully, size: {} bytes", pdf_data.len());
        
        Ok(pdf_data)
    }
    
    /// Build PDF print options from configuration
    fn build_pdf_options(&self, config: &PdfConfig) -> Result<PrintToPdfOptions, PdfError> {
        // Convert millimeters to inches (Chrome expects inches)
        let mm_to_inches = |mm: u32| (mm as f64) / 25.4;
        
        let mut options = PrintToPdfOptions::default();
        
        // Set page format
        match config.page_size.to_uppercase().as_str() {
            "A4" => {
                options.paper_width = Some(8.27); // A4 width in inches
                options.paper_height = Some(11.7); // A4 height in inches
            }
            "LETTER" => {
                options.paper_width = Some(8.5); // Letter width in inches
                options.paper_height = Some(11.0); // Letter height in inches
            }
            "LEGAL" => {
                options.paper_width = Some(8.5); // Legal width in inches
                options.paper_height = Some(14.0); // Legal height in inches
            }
            _ => {
                // Default to A4 for unknown formats
                warn!("Unknown page size '{}', defaulting to A4", config.page_size);
                options.paper_width = Some(8.27);
                options.paper_height = Some(11.7);
            }
        }
        
        // Set orientation
        options.landscape = Some(config.orientation == PageOrientation::Landscape);
        
        // Set margins
        options.margin_top = Some(mm_to_inches(config.margin_top));
        options.margin_right = Some(mm_to_inches(config.margin_right));
        options.margin_bottom = Some(mm_to_inches(config.margin_bottom));
        options.margin_left = Some(mm_to_inches(config.margin_left));
        
        // Set other options
        options.print_background = Some(config.print_background);
        options.scale = Some(config.scale as f64);
        options.prefer_css_page_size = Some(true);
        options.generate_tagged_pdf = Some(true); // For accessibility
        options.generate_document_outline = Some(true); // For navigation
        
        // Apply optimization settings
        // Note: Chrome's print_to_pdf doesn't directly support compression/image quality
        // These settings are applied through CSS and JavaScript optimization in render_html_to_pdf
        
        // Set metadata if provided
        if let Some(title) = &config.title {
            options.header_template = Some(format!(
                "<div style='font-size: 10px; text-align: center; width: 100%;'>{}</div>",
                title
            ));
            options.display_header_footer = Some(true);
        }
        
        debug!("PDF options configured: landscape={:?}, scale={:?}, compression={:?}, image_quality={:?}", 
               options.landscape, options.scale, config.compression_level, config.image_quality);
        
        Ok(options)
    }

    /// Apply optimization settings to HTML content
    fn optimize_html_content(&self, html_content: &str, config: &PdfConfig) -> String {
        let mut optimized_html = html_content.to_string();
        
        // Create optimization CSS
        let mut optimization_css = String::new();
        
        // Font optimization
        if config.embed_fonts {
            optimization_css.push_str(r#"
                @media print {
                    * {
                        font-display: block;
                        -webkit-font-smoothing: antialiased;
                        -moz-osx-font-smoothing: grayscale;
                    }
                    
                    /* Force font embedding */
                    @font-face {
                        font-family: 'system-fonts';
                        src: local('Arial'), local('Helvetica'), local('sans-serif');
                        font-display: block;
                    }
                }
            "#);
        } else {
            optimization_css.push_str(r#"
                @media print {
                    * {
                        font-family: Arial, Helvetica, sans-serif !important;
                    }
                }
            "#);
        }
        
        // Image quality optimization
        if let Some(quality) = config.image_quality {
            if quality < 80 {
                optimization_css.push_str(&format!(r#"
                    @media print {{
                        img {{
                            image-rendering: optimizeSpeed;
                            image-rendering: -webkit-optimize-contrast;
                            image-rendering: optimize-contrast;
                            -ms-interpolation-mode: nearest-neighbor;
                        }}
                    }}
                "#));
            } else {
                optimization_css.push_str(r#"
                    @media print {
                        img {
                            image-rendering: optimizeQuality;
                            image-rendering: -webkit-optimize-quality;
                            image-rendering: smooth;
                        }
                    }
                "#);
            }
        }
        
        // Compression optimization
        if let Some(compression) = config.compression_level {
            if compression > 6 {
                // High compression: simplify content
                optimization_css.push_str(r#"
                    @media print {
                        * {
                            box-shadow: none !important;
                            text-shadow: none !important;
                            filter: none !important;
                            transform: none !important;
                        }
                        
                        .gradient, .shadow, .blur {
                            background: none !important;
                            box-shadow: none !important;
                            filter: none !important;
                        }
                    }
                "#);
            }
        }
        
        // Inject optimization CSS
        if !optimization_css.is_empty() {
            let css_tag = format!("<style type=\"text/css\">{}</style>", optimization_css);
            
            // Try to inject before closing head tag, or at the beginning if no head
            if let Some(head_end) = optimized_html.find("</head>") {
                optimized_html.insert_str(head_end, &css_tag);
            } else if let Some(html_start) = optimized_html.find("<html") {
                if let Some(tag_end) = optimized_html[html_start..].find(">") {
                    let insert_pos = html_start + tag_end + 1;
                    optimized_html.insert_str(insert_pos, &css_tag);
                }
            } else {
                // Prepend to the entire content
                optimized_html = css_tag + &optimized_html;
            }
        }
        
        debug!("Applied optimization CSS for compression={:?}, image_quality={:?}, embed_fonts={}", 
               config.compression_level, config.image_quality, config.embed_fonts);
        
        optimized_html
    }
}

impl Default for PdfService {
    fn default() -> Self {
        Self::new()
    }
}

/// Generate a PDF from a template name and context using the provided TemplateService and PdfConfig
pub async fn template_to_pdf(
    template_service: &crate::services::template::TemplateService,
    template_name: &str,
    context: &serde_json::Value,
    pdf_service: &PdfService,
    pdf_config: &PdfConfig,
) -> Result<Vec<u8>, PdfError> {
    // Render the template to HTML
    let html = template_service
        .render_template(template_name, context)?;
    // Generate the PDF from the rendered HTML
    pdf_service
        .generate_pdf_from_html_with_config(&html, pdf_config)
        .await
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_pdf_config_default() {
        let config = PdfConfig::default();
        assert_eq!(config.page_size, "A4");
        assert_eq!(config.orientation, PageOrientation::Portrait);
        assert_eq!(config.margin_top, 20);
        assert_eq!(config.scale, 1.0);
        assert_eq!(config.timeout, Duration::from_secs(30));
        assert!(config.print_background);
        assert!(config.embed_fonts);
    }

    #[test]
    fn test_pdf_config_builder() {
        let config = PdfConfig::new()
            .with_page_size("Letter")
            .with_orientation(PageOrientation::Landscape)
            .with_margins(15)
            .with_scale(0.8)
            .with_timeout(Duration::from_secs(60))
            .with_compression(8)
            .with_image_quality(90)
            .with_metadata(
                Some("Test Invoice".to_string()),
                Some("Test Author".to_string()),
                None,
                Some(vec!["invoice".to_string(), "test".to_string()]),
            );

        assert_eq!(config.page_size, "Letter");
        assert_eq!(config.orientation, PageOrientation::Landscape);
        assert_eq!(config.margin_top, 15);
        assert_eq!(config.scale, 0.8);
        assert_eq!(config.timeout, Duration::from_secs(60));
        assert_eq!(config.compression_level, Some(8));
        assert_eq!(config.image_quality, Some(90));
        assert_eq!(config.title, Some("Test Invoice".to_string()));
        assert_eq!(config.author, Some("Test Author".to_string()));
    }

    #[test]
    fn test_pdf_config_validation() {
        // Valid configuration
        let config = PdfConfig::default();
        assert!(config.validate().is_ok());

        // Invalid scale
        let mut config = PdfConfig::default();
        config.scale = 3.0;
        assert!(config.validate().is_err());

        // Invalid timeout
        let mut config = PdfConfig::default();
        config.timeout = Duration::from_secs(0);
        assert!(config.validate().is_err());

        // Invalid margins
        let mut config = PdfConfig::default();
        config.margin_top = 150;
        assert!(config.validate().is_err());

        // Invalid compression
        let mut config = PdfConfig::default();
        config.compression_level = Some(15);
        assert!(config.validate().is_err());

        // Invalid image quality
        let mut config = PdfConfig::default();
        config.image_quality = Some(150);
        assert!(config.validate().is_err());
    }

    #[test]
    fn test_pdf_service_creation() {
        let service = PdfService::new();
        assert_eq!(service.default_config().page_size, "A4");

        let custom_config = PdfConfig::new().with_page_size("Letter");
        let service = PdfService::with_config(custom_config).unwrap();
        assert_eq!(service.default_config().page_size, "Letter");
    }

    #[test]
    fn test_pdf_service_invalid_config() {
        let mut invalid_config = PdfConfig::default();
        invalid_config.scale = 5.0; // Invalid scale
        assert!(PdfService::with_config(invalid_config).is_err());
    }

    #[test]
    fn test_scale_clamping() {
        let config = PdfConfig::new().with_scale(5.0); // Should be clamped to 2.0
        assert_eq!(config.scale, 2.0);

        let config = PdfConfig::new().with_scale(0.05); // Should be clamped to 0.1
        assert_eq!(config.scale, 0.1);
    }

    #[test]
    fn test_compression_clamping() {
        let config = PdfConfig::new().with_compression(15); // Should be clamped to 9
        assert_eq!(config.compression_level, Some(9));
    }

    #[test]
    fn test_image_quality_clamping() {
        let config = PdfConfig::new().with_image_quality(150); // Should be clamped to 100
        assert_eq!(config.image_quality, Some(100));
    }

    #[test]
    fn test_resource_tracker() {
        let mut tracker = ResourceTracker::new();
        assert!(!tracker.cleanup_completed.load(Ordering::Acquire));
        
        // Test that cleanup can be called multiple times safely
        tokio_test::block_on(async {
            tracker.cleanup().await;
            tracker.cleanup().await; // Should not panic or cause issues
        });
        
        assert!(tracker.cleanup_completed.load(Ordering::Acquire));
    }

    #[test]
    fn test_html_optimization_font_embedding() {
        let service = PdfService::new();
        let html = "<html><head></head><body>Test content</body></html>";
        
        // Test with font embedding enabled
        let config = PdfConfig::new();
        assert!(config.embed_fonts);
        let optimized = service.optimize_html_content(html, &config);
        assert!(optimized.contains("font-display: block"));
        assert!(optimized.contains("@font-face"));
        
        // Test with font embedding disabled
        let config = PdfConfig::new().with_metadata(None, None, None, None);
        let mut config = config;
        config.embed_fonts = false;
        let optimized = service.optimize_html_content(html, &config);
        assert!(optimized.contains("font-family: Arial, Helvetica, sans-serif !important"));
        assert!(!optimized.contains("@font-face"));
    }

    #[test]
    fn test_html_optimization_image_quality() {
        let service = PdfService::new();
        let html = "<html><head></head><body><img src='test.jpg'></body></html>";
        
        // Test with high image quality
        let config = PdfConfig::new().with_image_quality(95);
        let optimized = service.optimize_html_content(html, &config);
        assert!(optimized.contains("image-rendering: optimizeQuality"));
        assert!(optimized.contains("image-rendering: smooth"));
        
        // Test with low image quality (below 80 threshold)
        let config = PdfConfig::new().with_image_quality(70);
        let optimized = service.optimize_html_content(html, &config);
        assert!(optimized.contains("image-rendering: optimizeSpeed"));
        assert!(optimized.contains("optimize-contrast"));
    }

    #[test]
    fn test_html_optimization_compression() {
        let service = PdfService::new();
        let html = "<html><head></head><body><div class='gradient'>Test</div></body></html>";
        
        // Test with high compression
        let config = PdfConfig::new().with_compression(8);
        let optimized = service.optimize_html_content(html, &config);
        assert!(optimized.contains("box-shadow: none !important"));
        assert!(optimized.contains("text-shadow: none !important"));
        assert!(optimized.contains("filter: none !important"));
        
        // Test with low compression
        let config = PdfConfig::new().with_compression(3);
        let optimized = service.optimize_html_content(html, &config);
        // Should not contain aggressive optimization CSS
        assert!(!optimized.contains("box-shadow: none !important"));
    }

    #[test]
    fn test_html_optimization_css_injection() {
        let service = PdfService::new();
        
        // Test HTML with head tag
        let html_with_head = "<html><head><title>Test</title></head><body>Content</body></html>";
        let config = PdfConfig::new();
        let optimized = service.optimize_html_content(html_with_head, &config);
        assert!(optimized.contains("<style type=\"text/css\">"));
        let head_end_pos = optimized.find("</head>").unwrap();
        let style_pos = optimized.find("<style type=\"text/css\">").unwrap();
        assert!(style_pos < head_end_pos); // Style should be before </head>
        
        // Test HTML without head tag but with html tag
        let html_without_head = "<html><body>Content</body></html>";
        let optimized = service.optimize_html_content(html_without_head, &config);
        assert!(optimized.contains("<style type=\"text/css\">"));
        
        // Test plain HTML content
        let plain_html = "<div>Plain content</div>";
        let optimized = service.optimize_html_content(plain_html, &config);
        assert!(optimized.contains("<style type=\"text/css\">"));
        assert!(optimized.starts_with("<style type=\"text/css\">"));
    }

    #[test]
    fn test_optimization_config_defaults() {
        let config = PdfConfig::default();
        assert_eq!(config.compression_level, Some(6));
        assert_eq!(config.image_quality, Some(85));
        assert!(config.embed_fonts);
        
        let service = PdfService::new();
        let html = "<html><body>Test</body></html>";
        let optimized = service.optimize_html_content(html, &config);
        
        // Should apply default optimizations
        assert!(optimized.contains("font-display: block")); // embed_fonts=true
        assert!(optimized.contains("image-rendering: optimizeQuality")); // quality=85 (>90 is false, but 85 should still be good quality)
        assert!(!optimized.contains("box-shadow: none")); // compression=6 (not >6)
    }
} 