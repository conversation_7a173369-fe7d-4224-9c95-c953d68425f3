// Validation service - placeholder
// Will be implemented in Task 4 

use chrono::{NaiveDate, Utc};
use regex::Regex;
use std::collections::HashSet;
use std::sync::OnceLock;
use thiserror::Error;

/// Custom validation error type
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub enum ValidationError {
    #[error("Invalid email format: {0}")]
    InvalidEmail(String),
    
    #[error("Invalid currency code: {0}. Must be a valid ISO 4217 code")]
    InvalidCurrency(String),
    
    #[error("Invalid date: {0}")]
    InvalidDate(String),
    
    #[error("Field length error: {0}")]
    InvalidLength(String),
    
    #[error("Invalid range: {0}")]
    InvalidRange(String),
    
    #[error("Business rule violation: {0}")]
    BusinessRule(String),
}

/// Result type for validation operations
pub type ValidationResult<T = ()> = Result<T, ValidationError>;

/// Centralized validation service
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct ValidationService;

impl ValidationService {
    /// Create a new validation service instance
    pub fn new() -> Self {
        Self
    }

    /// Validate email address format
    pub fn validate_email(&self, email: &str) -> ValidationResult {
        static EMAIL_REGEX: OnceLock<Regex> = OnceLock::new();
        let regex = EMAIL_REGEX.get_or_init(|| {
            Regex::new(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$").unwrap()
        });

        if email.is_empty() {
            return Err(ValidationError::InvalidEmail("Email cannot be empty".to_string()));
        }

        if !regex.is_match(email) {
            return Err(ValidationError::InvalidEmail(format!("Invalid email format: {}", email)));
        }

        Ok(())
    }

    /// Validate currency code against ISO 4217 standard
    pub fn validate_currency_code(&self, currency: &str) -> ValidationResult {
        static VALID_CURRENCIES: OnceLock<HashSet<&'static str>> = OnceLock::new();
        let currencies = VALID_CURRENCIES.get_or_init(|| {
            [
                "USD", "EUR", "GBP", "JPY", "AUD", "CAD", "CHF", "CNY", "SEK", "NZD",
                "MXN", "SGD", "HKD", "NOK", "ZAR", "TRY", "BRL", "INR", "KRW", "PLN",
                "THB", "IDR", "HUF", "CZK", "ILS", "CLP", "PHP", "AED", "COP", "SAR",
                "MYR", "RON", "BGN", "HRK", "RUB", "UAH", "EGP", "QAR", "KWD", "BHD",
                "OMR", "JOD", "LBP", "TND", "DZD", "MAD", "KES", "UGX", "TZS", "ZMW",
                "BWP", "MUR", "SCR", "MGA", "XOF", "XAF", "GHS", "NGN", "ETB", "RWF",
                "DKK", "ISK", "CRC", "GTQ", "HNL", "NIO", "PAB", "PYG", "PEN", "UYU",
                "VES", "BOB", "ALL", "BAM", "MKD", "RSD", "MDL", "GEL", "AMD", "AZN",
                "BYN", "KZT", "KGS", "TJS", "TMT", "UZS", "AFN", "PKR", "LKR", "BDT",
                "BTN", "NPR", "MVR", "MMK", "LAK", "KHR", "VND", "MNT", "TWD", "FJD"
            ].into_iter().collect()
        });

        if currency.len() != 3 {
            return Err(ValidationError::InvalidCurrency(
                format!("Currency code must be exactly 3 characters, got: {}", currency)
            ));
        }

        if !currencies.contains(currency) {
            return Err(ValidationError::InvalidCurrency(
                format!("Unknown currency code: {}", currency)
            ));
        }

        Ok(())
    }

    /// Validate date within reasonable bounds
    pub fn validate_date(&self, date: &NaiveDate) -> ValidationResult {
        let min_date = NaiveDate::from_ymd_opt(1900, 1, 1).unwrap();
        let max_date = Utc::now().date_naive() + chrono::Duration::days(36500); // ~100 years

        if *date < min_date {
            return Err(ValidationError::InvalidDate(
                format!("Date {} is too early (minimum: {})", date, min_date)
            ));
        }

        if *date > max_date {
            return Err(ValidationError::InvalidDate(
                format!("Date {} is too far in the future (maximum: {})", date, max_date)
            ));
        }

        Ok(())
    }

    /// Validate due date is not before invoice date
    pub fn validate_due_date(&self, invoice_date: &NaiveDate, due_date: &NaiveDate) -> ValidationResult {
        // First validate the due date itself
        self.validate_date(due_date)?;

        if *due_date < *invoice_date {
            return Err(ValidationError::BusinessRule(
                format!("Due date {} cannot be before invoice date {}", due_date, invoice_date)
            ));
        }

        Ok(())
    }

    /// Validate string length within specified bounds
    pub fn validate_length(&self, value: &str, field_name: &str, min: Option<usize>, max: Option<usize>) -> ValidationResult {
        let len = value.len();

        if let Some(min_len) = min {
            if len < min_len {
                return Err(ValidationError::InvalidLength(
                    format!("{} must be at least {} characters, got {}", field_name, min_len, len)
                ));
            }
        }

        if let Some(max_len) = max {
            if len > max_len {
                return Err(ValidationError::InvalidLength(
                    format!("{} must be at most {} characters, got {}", field_name, max_len, len)
                ));
            }
        }

        Ok(())
    }

    /// Validate numeric value within specified range
    pub fn validate_range<T>(&self, value: T, field_name: &str, min: Option<T>, max: Option<T>) -> ValidationResult
    where
        T: PartialOrd + std::fmt::Display + Copy,
    {
        if let Some(min_val) = min {
            if value < min_val {
                return Err(ValidationError::InvalidRange(
                    format!("{} must be at least {}, got {}", field_name, min_val, value)
                ));
            }
        }

        if let Some(max_val) = max {
            if value > max_val {
                return Err(ValidationError::InvalidRange(
                    format!("{} must be at most {}, got {}", field_name, max_val, value)
                ));
            }
        }

        Ok(())
    }

    /// Validate percentage value (0-100)
    pub fn validate_percentage(&self, value: f64, field_name: &str) -> ValidationResult {
        self.validate_range(value, field_name, Some(0.0), Some(100.0))
    }

    /// Validate positive amount
    pub fn validate_positive_amount(&self, value: f64, field_name: &str) -> ValidationResult {
        self.validate_range(value, field_name, Some(0.0), None)
    }

    /// Validate positive integer
    pub fn validate_positive_integer(&self, value: i32, field_name: &str) -> ValidationResult {
        self.validate_range(value, field_name, Some(1), None)
    }

    /// Validate phone number format (basic validation)
    pub fn validate_phone(&self, phone: &str) -> ValidationResult {
        static PHONE_REGEX: OnceLock<Regex> = OnceLock::new();
        let regex = PHONE_REGEX.get_or_init(|| {
            Regex::new(r"^[\+]?[\d\s\-\(\)]{10,20}$").unwrap()
        });

        if phone.is_empty() {
            return Err(ValidationError::InvalidLength("Phone number cannot be empty".to_string()));
        }

        if !regex.is_match(phone) {
            return Err(ValidationError::InvalidLength(
                format!("Invalid phone number format: {}", phone)
            ));
        }

        Ok(())
    }

    /// Validate discount rules
    pub fn validate_discount(&self, percentage: Option<f64>, price: Option<f64>) -> ValidationResult {
        match (percentage, price) {
            (Some(pct), None) => {
                self.validate_percentage(pct, "discount percentage")?;
            }
            (None, Some(amt)) => {
                self.validate_positive_amount(amt, "discount amount")?;
            }
            (Some(_), Some(_)) => {
                return Err(ValidationError::BusinessRule(
                    "Discount cannot have both percentage and fixed amount".to_string()
                ));
            }
            (None, None) => {
                return Err(ValidationError::BusinessRule(
                    "Discount must specify either percentage or fixed amount".to_string()
                ));
            }
        }

        Ok(())
    }

    /// Validate that a collection is not empty
    pub fn validate_not_empty<T>(&self, items: &[T], field_name: &str) -> ValidationResult {
        if items.is_empty() {
            return Err(ValidationError::InvalidLength(
                format!("{} cannot be empty", field_name)
            ));
        }
        Ok(())
    }

    /// Validate ABN/Tax ID format (basic validation)
    pub fn validate_abn(&self, abn: &str) -> ValidationResult {
        // Basic validation - should be 11 digits for Australian ABN
        static ABN_REGEX: OnceLock<Regex> = OnceLock::new();
        let regex = ABN_REGEX.get_or_init(|| {
            Regex::new(r"^\d{11}$").unwrap()
        });

        if abn.is_empty() {
            return Ok(()); // ABN is optional
        }

        if !regex.is_match(abn) {
            return Err(ValidationError::InvalidLength(
                format!("Invalid ABN format: {} (should be 11 digits)", abn)
            ));
        }

        Ok(())
    }

    /// Comprehensive validation for InvoiceRequest
    /// Validates all fields and business rules, returning detailed error information
    pub fn validate_invoice_request(&self, request: &crate::models::request::InvoiceRequest) -> Result<(), Vec<ValidationError>> {
        let mut errors = Vec::new();

        // Validate invoice date
        if let Err(e) = self.validate_date(&request.invoice_date) {
            errors.push(e);
        }

        // Validate due date if provided
        if let Some(due_date) = &request.due_date {
            if let Err(e) = self.validate_due_date(&request.invoice_date, due_date) {
                errors.push(e);
            }
        }

        // Validate currency code
        if let Err(e) = self.validate_currency_code(&request.currency) {
            errors.push(e);
        }

        // Validate tax percentage if provided
        if let Some(tax) = request.tax {
            if let Err(e) = self.validate_percentage(tax, "tax") {
                errors.push(e);
            }
        }

        // Validate GST percentage if provided
        if let Some(gst) = request.gst {
            if let Err(e) = self.validate_percentage(gst, "GST") {
                errors.push(e);
            }
        }

        // Validate shipping fee if provided
        if let Some(shipping_fee) = request.shipping_fee {
            if let Err(e) = self.validate_positive_amount(shipping_fee, "shipping fee") {
                errors.push(e);
            }
        }

        // Validate discount if provided
        if let Some(discount) = &request.discount {
            if let Err(e) = self.validate_discount(discount.percentage, discount.price) {
                errors.push(e);
            }
        }

        // Validate billed_to contact
        if let Err(mut contact_errors) = self.validate_contact(&request.billed_to, "billed_to") {
            errors.append(&mut contact_errors);
        }

        // Validate from contact
        if let Err(mut contact_errors) = self.validate_contact(&request.from, "from") {
            errors.append(&mut contact_errors);
        }

        // Validate items
        if let Err(e) = self.validate_not_empty(&request.items, "items") {
            errors.push(e);
        } else {
            // Validate each item
            for (index, item) in request.items.iter().enumerate() {
                if let Err(mut item_errors) = self.validate_invoice_item(item, index) {
                    errors.append(&mut item_errors);
                }
            }
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }

    /// Validate a contact structure
    fn validate_contact(&self, contact: &crate::models::request::Contact, field_name: &str) -> Result<(), Vec<ValidationError>> {
        let mut errors = Vec::new();

        // Validate name
        if let Err(e) = self.validate_length(&contact.name, &format!("{}.name", field_name), Some(1), Some(255)) {
            errors.push(e);
        }

        // Validate email if provided
        if let Some(email) = &contact.email {
            if !email.is_empty() {
                if let Err(e) = self.validate_email(email) {
                    errors.push(ValidationError::InvalidEmail(format!("{}.email: {}", field_name, e)));
                }
            }
        }

        // Validate phone if provided
        if let Some(phone) = &contact.phone {
            if !phone.is_empty() {
                if let Err(e) = self.validate_length(phone, &format!("{}.phone", field_name), None, Some(50)) {
                    errors.push(e);
                }
                if let Err(e) = self.validate_phone(phone) {
                    errors.push(ValidationError::InvalidLength(format!("{}.phone: {}", field_name, e)));
                }
            }
        }

        // Validate ABN if provided
        if let Some(abn) = &contact.abn {
            if !abn.is_empty() {
                if let Err(e) = self.validate_length(abn, &format!("{}.abn", field_name), None, Some(50)) {
                    errors.push(e);
                }
                if let Err(e) = self.validate_abn(abn) {
                    errors.push(ValidationError::BusinessRule(format!("{}.abn: {}", field_name, e)));
                }
            }
        }

        // Validate address if provided
        if let Some(address) = &contact.address {
            if let Err(mut address_errors) = self.validate_address(address, &format!("{}.address", field_name)) {
                errors.append(&mut address_errors);
            }
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }

    /// Validate an address structure
    fn validate_address(&self, address: &crate::models::request::Address, field_name: &str) -> Result<(), Vec<ValidationError>> {
        let mut errors = Vec::new();

        // Validate street
        if let Err(e) = self.validate_length(&address.street, &format!("{}.street", field_name), Some(1), Some(255)) {
            errors.push(e);
        }

        // Validate state
        if let Err(e) = self.validate_length(&address.state, &format!("{}.state", field_name), Some(1), Some(100)) {
            errors.push(e);
        }

        // Validate city
        if let Err(e) = self.validate_length(&address.city, &format!("{}.city", field_name), Some(1), Some(100)) {
            errors.push(e);
        }

        // Validate post_code
        if let Err(e) = self.validate_length(&address.post_code, &format!("{}.post_code", field_name), Some(1), Some(20)) {
            errors.push(e);
        }

        // Validate country
        if let Err(e) = self.validate_length(&address.country, &format!("{}.country", field_name), Some(1), Some(100)) {
            errors.push(e);
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }

    /// Validate an invoice item structure
    fn validate_invoice_item(&self, item: &crate::models::request::InvoiceItem, index: usize) -> Result<(), Vec<ValidationError>> {
        let mut errors = Vec::new();
        let field_prefix = format!("items[{}]", index);

        // Validate name
        if let Err(e) = self.validate_length(&item.name, &format!("{}.name", field_prefix), Some(1), Some(255)) {
            errors.push(e);
        }

        // Validate description if provided
        if let Some(description) = &item.description {
            if let Err(e) = self.validate_length(description, &format!("{}.description", field_prefix), None, Some(1000)) {
                errors.push(e);
            }
        }

        // Validate quantity
        if let Err(e) = self.validate_positive_integer(item.quantity, &format!("{}.quantity", field_prefix)) {
            errors.push(e);
        }

        // Validate price
        if let Err(e) = self.validate_positive_amount(item.price, &format!("{}.price", field_prefix)) {
            errors.push(e);
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }

    /// Convert validation errors to ErrorData for API responses
    pub fn errors_to_response(&self, errors: Vec<ValidationError>) -> crate::models::response::ErrorData {
        if errors.is_empty() {
            return crate::models::response::ErrorData::validation("Unknown validation error");
        }

        if errors.len() == 1 {
            // Single error - use the error message directly
            let error = &errors[0];
            crate::models::response::ErrorData::validation(error.to_string())
        } else {
            // Multiple errors - create a summary with details
            let summary = format!("Validation failed with {} errors", errors.len());
            let details = errors.iter()
                .enumerate()
                .map(|(i, err)| format!("{}. {}", i + 1, err))
                .collect::<Vec<_>>()
                .join("\n");
            
            crate::models::response::ErrorData::with_details("VALIDATION_ERROR", summary, details)
        }
    }
}

impl Default for ValidationService {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use chrono::NaiveDate;

    fn get_validator() -> ValidationService {
        ValidationService::new()
    }

    #[test]
    fn test_email_validation() {
        let validator = get_validator();

        // Valid emails
        assert!(validator.validate_email("<EMAIL>").is_ok());
        assert!(validator.validate_email("<EMAIL>").is_ok());
        assert!(validator.validate_email("<EMAIL>").is_ok());

        // Invalid emails
        assert!(validator.validate_email("").is_err());
        assert!(validator.validate_email("invalid").is_err());
        assert!(validator.validate_email("@domain.com").is_err());
        assert!(validator.validate_email("user@").is_err());
        assert!(validator.validate_email("user@domain").is_err());
    }

    #[test]
    fn test_currency_validation() {
        let validator = get_validator();

        // Valid currencies
        assert!(validator.validate_currency_code("USD").is_ok());
        assert!(validator.validate_currency_code("EUR").is_ok());
        assert!(validator.validate_currency_code("GBP").is_ok());
        assert!(validator.validate_currency_code("AUD").is_ok());

        // Invalid currencies
        assert!(validator.validate_currency_code("").is_err());
        assert!(validator.validate_currency_code("US").is_err());
        assert!(validator.validate_currency_code("USDX").is_err());
        assert!(validator.validate_currency_code("XXX").is_err());
        assert!(validator.validate_currency_code("123").is_err());
    }

    #[test]
    fn test_date_validation() {
        let validator = get_validator();

        // Valid dates
        let today = Utc::now().date_naive();
        assert!(validator.validate_date(&today).is_ok());
        
        let valid_past = NaiveDate::from_ymd_opt(2020, 1, 1).unwrap();
        assert!(validator.validate_date(&valid_past).is_ok());
        
        let valid_future = today + chrono::Duration::days(365);
        assert!(validator.validate_date(&valid_future).is_ok());

        // Invalid dates
        let too_old = NaiveDate::from_ymd_opt(1800, 1, 1).unwrap();
        assert!(validator.validate_date(&too_old).is_err());
        
        let too_far_future = today + chrono::Duration::days(50000);
        assert!(validator.validate_date(&too_far_future).is_err());
    }

    #[test]
    fn test_due_date_validation() {
        let validator = get_validator();

        let invoice_date = NaiveDate::from_ymd_opt(2024, 1, 1).unwrap();
        let valid_due = NaiveDate::from_ymd_opt(2024, 1, 31).unwrap();
        let invalid_due = NaiveDate::from_ymd_opt(2023, 12, 31).unwrap();

        assert!(validator.validate_due_date(&invoice_date, &valid_due).is_ok());
        assert!(validator.validate_due_date(&invoice_date, &invalid_due).is_err());
    }

    #[test]
    fn test_length_validation() {
        let validator = get_validator();

        // Valid lengths
        assert!(validator.validate_length("hello", "test", Some(3), Some(10)).is_ok());
        assert!(validator.validate_length("hi", "test", Some(2), Some(10)).is_ok());
        assert!(validator.validate_length("1234567890", "test", Some(2), Some(10)).is_ok());

        // Invalid lengths
        assert!(validator.validate_length("a", "test", Some(3), Some(10)).is_err());
        assert!(validator.validate_length("***********", "test", Some(2), Some(10)).is_err());
    }

    #[test]
    fn test_range_validation() {
        let validator = get_validator();

        // Valid ranges
        assert!(validator.validate_range(5.0, "test", Some(0.0), Some(10.0)).is_ok());
        assert!(validator.validate_range(0.0, "test", Some(0.0), Some(10.0)).is_ok());
        assert!(validator.validate_range(10.0, "test", Some(0.0), Some(10.0)).is_ok());

        // Invalid ranges
        assert!(validator.validate_range(-1.0, "test", Some(0.0), Some(10.0)).is_err());
        assert!(validator.validate_range(11.0, "test", Some(0.0), Some(10.0)).is_err());
    }

    #[test]
    fn test_percentage_validation() {
        let validator = get_validator();

        assert!(validator.validate_percentage(0.0, "tax").is_ok());
        assert!(validator.validate_percentage(50.0, "tax").is_ok());
        assert!(validator.validate_percentage(100.0, "tax").is_ok());

        assert!(validator.validate_percentage(-1.0, "tax").is_err());
        assert!(validator.validate_percentage(101.0, "tax").is_err());
    }

    #[test]
    fn test_positive_amount_validation() {
        let validator = get_validator();

        assert!(validator.validate_positive_amount(0.0, "price").is_ok());
        assert!(validator.validate_positive_amount(100.50, "price").is_ok());

        assert!(validator.validate_positive_amount(-0.01, "price").is_err());
    }

    #[test]
    fn test_positive_integer_validation() {
        let validator = get_validator();

        assert!(validator.validate_positive_integer(1, "quantity").is_ok());
        assert!(validator.validate_positive_integer(100, "quantity").is_ok());

        assert!(validator.validate_positive_integer(0, "quantity").is_err());
        assert!(validator.validate_positive_integer(-1, "quantity").is_err());
    }

    #[test]
    fn test_phone_validation() {
        let validator = get_validator();

        // Valid phones
        assert!(validator.validate_phone("+1234567890").is_ok());
        assert!(validator.validate_phone("**************").is_ok());
        assert!(validator.validate_phone("(*************").is_ok());

        // Invalid phones
        assert!(validator.validate_phone("").is_err());
        assert!(validator.validate_phone("123").is_err());
        assert!(validator.validate_phone("abc-def-ghij").is_err());
    }

    #[test]
    fn test_discount_validation() {
        let validator = get_validator();

        // Valid discounts
        assert!(validator.validate_discount(Some(10.0), None).is_ok());
        assert!(validator.validate_discount(None, Some(50.0)).is_ok());

        // Invalid discounts
        assert!(validator.validate_discount(Some(10.0), Some(50.0)).is_err()); // Both set
        assert!(validator.validate_discount(None, None).is_err()); // Neither set
        assert!(validator.validate_discount(Some(-5.0), None).is_err()); // Invalid percentage
        assert!(validator.validate_discount(Some(150.0), None).is_err()); // Invalid percentage
        assert!(validator.validate_discount(None, Some(-10.0)).is_err()); // Invalid amount
    }

    #[test]
    fn test_not_empty_validation() {
        let validator = get_validator();

        let empty_vec: Vec<i32> = vec![];
        let non_empty_vec = vec![1, 2, 3];

        assert!(validator.validate_not_empty(&non_empty_vec, "items").is_ok());
        assert!(validator.validate_not_empty(&empty_vec, "items").is_err());
    }

    #[test]
    fn test_abn_validation() {
        let validator = get_validator();

        // Valid ABNs
        assert!(validator.validate_abn("***********").is_ok());
        assert!(validator.validate_abn("***********").is_ok());
        assert!(validator.validate_abn("").is_ok()); // Empty is OK (optional)

        // Invalid ABNs
        assert!(validator.validate_abn("123456789").is_err()); // Too short
        assert!(validator.validate_abn("***********2").is_err()); // Too long
        assert!(validator.validate_abn("1234567890a").is_err()); // Contains letter
    }

    // Helper function to create a valid invoice request for testing
    fn create_valid_invoice_request() -> crate::models::request::InvoiceRequest {
        use crate::models::request::*;

        InvoiceRequest {
            template_id: Some("default".to_string()),
            invoice_date: NaiveDate::from_ymd_opt(2024, 1, 15).unwrap(),
            due_date: Some(NaiveDate::from_ymd_opt(2024, 2, 15).unwrap()),
            currency: "USD".to_string(),
            tax: Some(10.0),
            gst: Some(10.0),
            shipping_fee: Some(25.0),
            discount: Some(Discount {
                percentage: Some(5.0),
                price: None,
            }),
            billed_to: Contact {
                name: "Acme Corp".to_string(),
                email: Some("<EMAIL>".to_string()),
                phone: Some("+1234567890".to_string()),
                abn: Some("***********".to_string()),
                address: Some(Address {
                    street: "123 Business St".to_string(),
                    state: "CA".to_string(),
                    city: "San Francisco".to_string(),
                    post_code: "94102".to_string(),
                    country: "USA".to_string(),
                }),
            },
            from: Contact {
                name: "My Company".to_string(),
                email: Some("<EMAIL>".to_string()),
                phone: Some("+1987654321".to_string()),
                abn: Some("***********".to_string()),
                address: Some(Address {
                    street: "456 Office Ave".to_string(),
                    state: "CA".to_string(),
                    city: "Los Angeles".to_string(),
                    post_code: "90210".to_string(),
                    country: "USA".to_string(),
                }),
            },
            items: vec![
                InvoiceItem {
                    name: "Product A".to_string(),
                    description: Some("High quality product".to_string()),
                    quantity: 2,
                    price: 100.0,
                },
                InvoiceItem {
                    name: "Service B".to_string(),
                    description: None,
                    quantity: 1,
                    price: 50.0,
                },
            ],
        }
    }

    #[test]
    fn test_validate_invoice_request_success() {
        let validator = get_validator();
        let request = create_valid_invoice_request();

        let result = validator.validate_invoice_request(&request);
        assert!(result.is_ok(), "Valid invoice request should pass validation");
    }

    #[test]
    fn test_validate_invoice_request_invalid_currency() {
        let validator = get_validator();
        let mut request = create_valid_invoice_request();
        request.currency = "INVALID".to_string();

        let result = validator.validate_invoice_request(&request);
        assert!(result.is_err());
        
        let errors = result.unwrap_err();
        assert_eq!(errors.len(), 1);
        assert!(matches!(errors[0], ValidationError::InvalidCurrency(_)));
    }

    #[test]
    fn test_validate_invoice_request_invalid_due_date() {
        let validator = get_validator();
        let mut request = create_valid_invoice_request();
        // Set due date before invoice date
        request.due_date = Some(NaiveDate::from_ymd_opt(2024, 1, 1).unwrap());

        let result = validator.validate_invoice_request(&request);
        assert!(result.is_err());
        
        let errors = result.unwrap_err();
        assert_eq!(errors.len(), 1);
        assert!(matches!(errors[0], ValidationError::BusinessRule(_)));
    }

    #[test]
    fn test_validate_invoice_request_invalid_percentages() {
        let validator = get_validator();
        let mut request = create_valid_invoice_request();
        request.tax = Some(150.0); // Invalid tax percentage
        request.gst = Some(-5.0);  // Invalid GST percentage

        let result = validator.validate_invoice_request(&request);
        assert!(result.is_err());
        
        let errors = result.unwrap_err();
        assert_eq!(errors.len(), 2);
        assert!(errors.iter().all(|e| matches!(e, ValidationError::InvalidRange(_))));
    }

    #[test]
    fn test_validate_invoice_request_invalid_contact_email() {
        let validator = get_validator();
        let mut request = create_valid_invoice_request();
        request.billed_to.email = Some("invalid-email".to_string());

        let result = validator.validate_invoice_request(&request);
        assert!(result.is_err());
        
        let errors = result.unwrap_err();
        assert_eq!(errors.len(), 1);
        assert!(matches!(errors[0], ValidationError::InvalidEmail(_)));
    }

    #[test]
    fn test_validate_invoice_request_empty_items() {
        let validator = get_validator();
        let mut request = create_valid_invoice_request();
        request.items = vec![];

        let result = validator.validate_invoice_request(&request);
        assert!(result.is_err());
        
        let errors = result.unwrap_err();
        assert_eq!(errors.len(), 1);
        assert!(matches!(errors[0], ValidationError::InvalidLength(_)));
    }

    #[test]
    fn test_validate_invoice_request_invalid_item_quantity() {
        let validator = get_validator();
        let mut request = create_valid_invoice_request();
        request.items[0].quantity = 0; // Invalid quantity

        let result = validator.validate_invoice_request(&request);
        assert!(result.is_err());
        
        let errors = result.unwrap_err();
        assert_eq!(errors.len(), 1);
        assert!(matches!(errors[0], ValidationError::InvalidRange(_)));
    }

    #[test]
    fn test_validate_invoice_request_invalid_item_price() {
        let validator = get_validator();
        let mut request = create_valid_invoice_request();
        request.items[0].price = -10.0; // Invalid price

        let result = validator.validate_invoice_request(&request);
        assert!(result.is_err());
        
        let errors = result.unwrap_err();
        assert_eq!(errors.len(), 1);
        assert!(matches!(errors[0], ValidationError::InvalidRange(_)));
    }

    #[test]
    fn test_validate_invoice_request_multiple_errors() {
        let validator = get_validator();
        let mut request = create_valid_invoice_request();
        
        // Introduce multiple errors
        request.currency = "INVALID".to_string();
        request.tax = Some(150.0);
        request.billed_to.email = Some("invalid-email".to_string());
        request.items[0].quantity = 0;

        let result = validator.validate_invoice_request(&request);
        assert!(result.is_err());
        
        let errors = result.unwrap_err();
        assert_eq!(errors.len(), 4);
    }

    #[test]
    fn test_validate_invoice_request_missing_required_fields() {
        let validator = get_validator();
        let mut request = create_valid_invoice_request();
        
        // Clear required fields
        request.billed_to.name = "".to_string();
        request.from.name = "".to_string();
        request.items[0].name = "".to_string();

        let result = validator.validate_invoice_request(&request);
        assert!(result.is_err());
        
        let errors = result.unwrap_err();
        assert_eq!(errors.len(), 3);
        assert!(errors.iter().all(|e| matches!(e, ValidationError::InvalidLength(_))));
    }

    #[test]
    fn test_validate_invoice_request_invalid_address() {
        let validator = get_validator();
        let mut request = create_valid_invoice_request();
        
        // Make address invalid
        if let Some(ref mut address) = request.billed_to.address {
            address.street = "".to_string(); // Empty required field
            address.post_code = "***********2345678901".to_string(); // Too long
        }

        let result = validator.validate_invoice_request(&request);
        assert!(result.is_err());
        
        let errors = result.unwrap_err();
        assert_eq!(errors.len(), 2);
        assert!(errors.iter().all(|e| matches!(e, ValidationError::InvalidLength(_))));
    }

    #[test]
    fn test_errors_to_response_single_error() {
        let validator = get_validator();
        let errors = vec![ValidationError::InvalidEmail("test error".to_string())];
        
        let response = validator.errors_to_response(errors);
        assert_eq!(response.code, "VALIDATION_ERROR");
        assert!(response.message.contains("test error"));
        assert!(response.details.is_none());
    }

    #[test]
    fn test_errors_to_response_multiple_errors() {
        let validator = get_validator();
        let errors = vec![
            ValidationError::InvalidEmail("email error".to_string()),
            ValidationError::InvalidCurrency("currency error".to_string()),
        ];
        
        let response = validator.errors_to_response(errors);
        assert_eq!(response.code, "VALIDATION_ERROR");
        assert!(response.message.contains("2 errors"));
        assert!(response.details.is_some());
        
        let details = response.details.unwrap();
        assert!(details.contains("1. Invalid email format: email error"));
        assert!(details.contains("2. Invalid currency code: currency error"));
    }

    #[test]
    fn test_errors_to_response_empty_errors() {
        let validator = get_validator();
        let errors = vec![];
        
        let response = validator.errors_to_response(errors);
        assert_eq!(response.code, "VALIDATION_ERROR");
        assert!(response.message.contains("Unknown validation error"));
    }
} 