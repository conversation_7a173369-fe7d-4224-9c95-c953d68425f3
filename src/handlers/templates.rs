use axum::{
    extract::State,
    response::<PERSON><PERSON>,
};
use serde::{Deserialize, Serialize};
use tracing::{info, error};

use crate::{
    AppState,
    error::AppError,
    models::response::TemplateInfo,
};

/// Response structure for template list endpoint
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct TemplateListResponse {
    /// Indicates if the operation was successful
    pub success: bool,

    /// Template data when successful
    pub data: Option<TemplateListData>,

    /// Error information when unsuccessful
    pub error: Option<String>,
}

/// Template list data structure
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct TemplateListData {
    /// List of available templates
    pub templates: Vec<TemplateInfo>,
}

/// Get information about available invoice templates
///
/// This endpoint returns metadata about all available invoice templates,
/// including their IDs, names, descriptions, and preview URLs.
/// For MVP, returns information about the single default template.
pub async fn list_templates(
    State(state): State<AppState>,
) -> Result<Json<TemplateListResponse>, AppError> {
    info!("Template list request received");

    // Get available template IDs from the template service
    let template_ids = state.services.template.list_templates();

    info!("Found {} available templates", template_ids.len());

    // Create template info for each available template
    let mut templates = Vec::new();

    for template_id in template_ids {
        let template_info = match template_id.as_str() {
            "default" => TemplateInfo {
                id: "default".to_string(),
                name: "Default Template".to_string(),
                description: "Standard invoice template with clean, professional layout".to_string(),
                preview_url: None, // Future enhancement: add preview image URLs
            },
            // Future templates can be added here
            _ => {
                error!("Unknown template ID found: {}", template_id);
                continue; // Skip unknown templates
            }
        };

        templates.push(template_info);
    }

    // Ensure we always have at least the default template
    if templates.is_empty() {
        error!("No templates found, adding default template as fallback");
        templates.push(TemplateInfo {
            id: "default".to_string(),
            name: "Default Template".to_string(),
            description: "Standard invoice template with clean, professional layout".to_string(),
            preview_url: None,
        });
    }

    info!("Returning {} template(s) in response", templates.len());

    let response = TemplateListResponse {
        success: true,
        data: Some(TemplateListData { templates }),
        error: None,
    };

    Ok(Json(response))
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::services::template::TemplateService;

    #[test]
    fn test_template_list_response_serialization() {
        let template = TemplateInfo {
            id: "test".to_string(),
            name: "Test Template".to_string(),
            description: "A test template".to_string(),
            preview_url: Some("https://example.com/preview.png".to_string()),
        };

        let response = TemplateListResponse {
            success: true,
            data: Some(TemplateListData {
                templates: vec![template],
            }),
            error: None,
        };

        let json = serde_json::to_string(&response).unwrap();

        // Verify camelCase conversion
        assert!(json.contains("previewUrl"));
        assert!(json.contains("\"success\":true"));
        assert!(json.contains("\"templates\":["));
    }

    #[test]
    fn test_template_list_data_structure() {
        let templates = vec![
            TemplateInfo {
                id: "default".to_string(),
                name: "Default Template".to_string(),
                description: "Standard invoice template".to_string(),
                preview_url: None,
            },
            TemplateInfo {
                id: "modern".to_string(),
                name: "Modern Template".to_string(),
                description: "Modern invoice template".to_string(),
                preview_url: Some("https://example.com/modern.png".to_string()),
            },
        ];

        let data = TemplateListData { templates };

        assert_eq!(data.templates.len(), 2);
        assert_eq!(data.templates[0].id, "default");
        assert_eq!(data.templates[1].id, "modern");
        assert!(data.templates[1].preview_url.is_some());
    }

    #[test]
    fn test_template_service_integration() {
        // Test that we can create a template service and get template list
        let template_service = TemplateService::new().expect("Failed to create template service");
        let template_ids = template_service.list_templates();

        // Should have at least the default template
        assert!(!template_ids.is_empty());
        assert!(template_ids.contains(&"default".to_string()));
    }

    #[test]
    fn test_response_format_matches_api_documentation() {
        // Create a response that matches the expected API format from README.md
        let template = TemplateInfo {
            id: "default".to_string(),
            name: "Default Template".to_string(),
            description: "Standard invoice template".to_string(),
            preview_url: None,
        };

        let response = TemplateListResponse {
            success: true,
            data: Some(TemplateListData {
                templates: vec![template],
            }),
            error: None,
        };

        let json = serde_json::to_string_pretty(&response).unwrap();

        // Parse back to verify structure
        let parsed: serde_json::Value = serde_json::from_str(&json).unwrap();

        // Verify top-level structure
        assert_eq!(parsed["success"], true);
        assert!(parsed["data"].is_object());
        assert!(parsed["error"].is_null());

        // Verify data structure
        let data = &parsed["data"];
        assert!(data["templates"].is_array());

        // Verify template structure
        let templates = data["templates"].as_array().unwrap();
        assert_eq!(templates.len(), 1);

        let template = &templates[0];
        assert_eq!(template["id"], "default");
        assert_eq!(template["name"], "Default Template");
        assert_eq!(template["description"], "Standard invoice template");
        assert!(template["previewUrl"].is_null()); // Check camelCase conversion

        // Verify the JSON structure matches the documented API format
        println!("Generated JSON response:\n{}", json);
    }
}

// Integration tests that require database connection
#[cfg(test)]
mod integration_tests {
    use super::*;
    use crate::{
        config::Config,
        state::AppState,
    };
    use axum::{
        body::Body,
        extract::State,
        http::{Request, StatusCode},
        routing::get,
        Router,
    };
    use tower::util::ServiceExt;

    async fn create_test_state() -> AppState {
        let config = Config::load().expect("Failed to load test config");
        AppState::new(config).await.expect("Failed to create test state")
    }

    #[tokio::test]
    #[ignore] // Use `cargo test -- --ignored` to run this test
    async fn test_list_templates_success() {
        let state = create_test_state().await;

        let result = list_templates(State(state)).await;

        assert!(result.is_ok());
        let response = result.unwrap().0;

        assert!(response.success);
        assert!(response.data.is_some());
        assert!(response.error.is_none());

        let data = response.data.unwrap();
        assert!(!data.templates.is_empty());

        // Check that default template is present
        let default_template = data.templates.iter()
            .find(|t| t.id == "default")
            .expect("Default template should be present");

        assert_eq!(default_template.name, "Default Template");
        assert!(!default_template.description.is_empty());
        assert!(default_template.preview_url.is_none());
    }

    #[tokio::test]
    #[ignore] // Use `cargo test -- --ignored` to run this test
    async fn test_templates_endpoint_integration() {
        let state = create_test_state().await;

        let app = Router::new()
            .route("/api/v1/templates", get(list_templates))
            .with_state(state);

        let request = Request::builder()
            .uri("/api/v1/templates")
            .body(Body::empty())
            .unwrap();

        let response = app.oneshot(request).await.unwrap();

        assert_eq!(response.status(), StatusCode::OK);

        let body = axum::body::to_bytes(response.into_body(), usize::MAX).await.unwrap();
        let response_json: TemplateListResponse = serde_json::from_slice(&body).unwrap();

        assert!(response_json.success);
        assert!(response_json.data.is_some());
        assert!(!response_json.data.unwrap().templates.is_empty());
    }
}