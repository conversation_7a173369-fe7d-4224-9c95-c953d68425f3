use axum::{
    extract::{ConnectInfo, Json, State},
    response::<PERSON><PERSON> as ResponseJson,
};
use rust_decimal::Decimal;
use std::net::SocketAddr;
use tracing::{error, info};
use uuid::Uuid;
use validator::Validate;

use crate::{
    error::AppError,
    middleware::security::sanitization,
    models::{
        request::InvoiceRequest,
        response::{InvoiceData, InvoiceResponse},
    },
    repositories::{
        invoice_repository::{CreateInvoiceData, InvoiceRepository},
        invoice_item_repository::{CreateInvoiceItemData, InvoiceItemRepository},
    },
    state::AppState,
    utils,
};

/// Generate invoice PDF and return download URL
pub async fn generate_invoice(
    ConnectInfo(addr): ConnectInfo<SocketAddr>,
    State(state): State<AppState>,
    <PERSON><PERSON>(request): <PERSON><PERSON><InvoiceRequest>,
) -> Result<ResponseJson<InvoiceResponse>, AppError> {
    info!("Invoice generation request received");

    // Extract client IP
    let client_ip = addr.ip().to_string();

    // 1. Validate request data
    request.validate()
        .map_err(|e| AppError::Validation(format!("Request validation failed: {}", e)))?;

    info!("Request validation passed for IP: {}", sanitization::sanitize_for_logging(&client_ip));

    // 2. Generate invoice ID
    let invoice_id = Uuid::new_v4();
    let invoice_id_str = invoice_id.to_string();

    // 3. Calculate totals
    let (_subtotal, tax_amount, gst_amount, discount_amount, total_amount) =
        calculate_invoice_totals(&request)?;

    info!("Invoice totals calculated - Total: {}", total_amount);

    // 4. Render HTML template
    let html_content = state.services.template
        .render_invoice(&request)
        .map_err(|e| {
            error!("Template rendering failed: {}", e);
            AppError::Template(e)
        })?;

    info!("HTML template rendered successfully");

    // 5. Generate PDF from HTML
    let pdf_bytes = state.services.pdf
        .generate_pdf_from_html(&html_content)
        .await
        .map_err(|e| {
            error!("PDF generation failed: {}", e);
            AppError::Pdf(e)
        })?;

    let file_size = pdf_bytes.len() as i64;
    info!("PDF generated successfully - Size: {} bytes", file_size);

    // 6. Upload PDF to R2 storage
    let file_key = utils::generate_pdf_filename(&invoice_id_str);
    let file_path = state.services.storage
        .upload_pdf(pdf_bytes, file_key.clone())
        .await
        .map_err(|e| {
            error!("Storage upload failed: {}", e);
            AppError::Storage(e)
        })?;

    info!("PDF uploaded to storage: {}", file_path);

    // 7. Save invoice metadata to database
    let expires_at = utils::calculate_expiration_date(7); // 7 days
    let invoice_data = CreateInvoiceData {
        template_id: request.template_id.clone(),
        invoice_date: request.invoice_date,
        due_date: request.due_date,
        currency: request.currency.clone(),
        total_amount,
        tax_amount,
        gst_amount,
        shipping_fee: request.shipping_fee.map(Decimal::from_f64_retain).flatten(),
        discount_amount,
        billed_to_name: request.billed_to.name.clone(),
        billed_to_email: request.billed_to.email.clone(),
        from_name: request.from.name.clone(),
        from_email: request.from.email.clone(),
        file_path: Some(file_path.clone()),
        file_size: Some(file_size),
        expires_at,
        ip_address: client_ip.clone(),
    };

    let invoice = InvoiceRepository::create(&state.database.get_connection(), invoice_data)
        .await
        .map_err(|e| {
            error!("Database save failed: {}", e);
            AppError::Database(e)
        })?;

    info!("Invoice metadata saved to database: {}", invoice.id);

    // 8. Save invoice items to database
    let item_data: Vec<CreateInvoiceItemData> = request.items.iter().map(|item| {
        CreateInvoiceItemData {
            invoice_id: invoice.id,
            name: item.name.clone(),
            description: item.description.clone(),
            quantity: item.quantity,
            price: Decimal::from_f64_retain(item.price).unwrap_or_default(),
        }
    }).collect();

    InvoiceItemRepository::create_batch(&state.database.get_connection(), item_data)
        .await
        .map_err(|e| {
            error!("Invoice items save failed: {}", e);
            AppError::Database(e)
        })?;

    info!("Invoice items saved to database");

    // 9. Generate pre-signed URL for download
    let download_url = state.services.storage
        .generate_presigned_url(&file_path)
        .await
        .map_err(|e| {
            error!("Pre-signed URL generation failed: {}", e);
            AppError::Storage(e)
        })?;

    info!("Pre-signed URL generated successfully");

    // 10. Return successful response
    let response_data = InvoiceData {
        invoice_id: invoice_id_str,
        download_url,
        expires_at,
        file_size: file_size as u64,
        created_at: invoice.created_at.into(),
    };

    info!("Invoice generation completed successfully");
    Ok(ResponseJson(InvoiceResponse::success(response_data)))
}

/// Calculate invoice totals including tax, GST, discount, and final amount
fn calculate_invoice_totals(request: &InvoiceRequest) -> Result<(Decimal, Option<Decimal>, Option<Decimal>, Option<Decimal>, Decimal), AppError> {
    // Calculate subtotal from items
    let subtotal = request.items.iter()
        .map(|item| {
            let price = Decimal::from_f64_retain(item.price).unwrap_or_default();
            let quantity = Decimal::from(item.quantity);
            price * quantity
        })
        .sum::<Decimal>();

    // Calculate shipping fee
    let shipping_fee = request.shipping_fee
        .map(Decimal::from_f64_retain)
        .flatten()
        .unwrap_or_default();

    // Calculate discount amount
    let discount_amount = if let Some(discount) = &request.discount {
        if let Some(percentage) = discount.percentage {
            Some(subtotal * Decimal::from_f64_retain(percentage / 100.0).unwrap_or_default())
        } else if let Some(price) = discount.price {
            Decimal::from_f64_retain(price)
        } else {
            None
        }
    } else {
        None
    };

    // Calculate taxable amount (subtotal + shipping - discount)
    let taxable_amount = subtotal + shipping_fee - discount_amount.unwrap_or_default();

    // Calculate tax amount
    let tax_amount = request.tax
        .map(|rate| taxable_amount * Decimal::from_f64_retain(rate / 100.0).unwrap_or_default());

    // Calculate GST amount
    let gst_amount = request.gst
        .map(|rate| taxable_amount * Decimal::from_f64_retain(rate / 100.0).unwrap_or_default());

    // Calculate total amount
    let total_amount = taxable_amount +
        tax_amount.unwrap_or_default() +
        gst_amount.unwrap_or_default();

    Ok((subtotal, tax_amount, gst_amount, discount_amount, total_amount))
}

