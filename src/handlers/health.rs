use axum::{
    extract::State,
    http::StatusCode,
    response::<PERSON><PERSON>,
};
use tracing::{debug, error, info, warn};
use std::time::Duration;

use crate::{
    models::response::HealthCheckResponse,
    state::AppState,
};

/// Health check endpoint that verifies all service dependencies
///
/// This endpoint performs comprehensive health checks on:
/// - Database connectivity
/// - R2 storage connectivity
/// - PDF generation service
/// - Overall service status
///
/// Returns appropriate HTTP status codes:
/// - 200: All services healthy
/// - 503: One or more critical services unhealthy
/// - 206: Some services degraded but core functionality available
pub async fn health_check(State(state): State<AppState>) -> Result<(StatusCode, Json<HealthCheckResponse>), StatusCode> {
    info!("Starting comprehensive health check");
    
    let service_name = "invoice-generator-api";
    let version = env!("CARGO_PKG_VERSION");
    
    // Perform health checks for all dependencies
    let database_status = check_database_health(&state).await;
    let storage_status = check_storage_health(&state).await;
    let pdf_status = check_pdf_service_health(&state).await;
    
    // Determine overall service status
    let overall_status = determine_overall_status(&database_status, &storage_status, &pdf_status);
    
    // Create response based on overall status
    let response = match overall_status.as_str() {
        "healthy" => {
            info!("Health check completed: All services healthy");
            HealthCheckResponse::healthy(service_name, version)
        },
        "degraded" => {
            warn!("Health check completed: Some services degraded");
            HealthCheckResponse::degraded(service_name, version, &database_status, &storage_status)
        },
        "unhealthy" => {
            error!("Health check completed: Critical services unhealthy");
            HealthCheckResponse::unhealthy(service_name, version, &database_status, &storage_status)
        },
        _ => {
            error!("Health check completed: Unknown status");
            HealthCheckResponse::unhealthy(service_name, version, "unknown", "unknown")
        }
    };
    
    // Return appropriate HTTP status code
    let status_code = match overall_status.as_str() {
        "healthy" => StatusCode::OK,
        "degraded" => StatusCode::PARTIAL_CONTENT, // 206 - Partial Content indicates degraded service
        "unhealthy" => StatusCode::SERVICE_UNAVAILABLE, // 503 - Service Unavailable
        _ => StatusCode::INTERNAL_SERVER_ERROR,
    };

    debug!("Health check response: status={}, database={}, storage={}, pdf={}",
           overall_status, database_status, storage_status, pdf_status);

    // Return the status code and JSON response as a tuple
    Ok((status_code, Json(response)))
}

/// Check database connectivity and health
async fn check_database_health(state: &AppState) -> String {
    debug!("Checking database health");
    
    match state.database.test_connection().await {
        Ok(_) => {
            debug!("Database health check passed");
            "healthy".to_string()
        },
        Err(e) => {
            error!("Database health check failed: {}", e);
            "unhealthy".to_string()
        }
    }
}

/// Check R2 storage connectivity and health
async fn check_storage_health(state: &AppState) -> String {
    debug!("Checking storage health");
    
    match state.services.storage.health_check().await {
        Ok(_) => {
            debug!("Storage health check passed");
            "healthy".to_string()
        },
        Err(e) => {
            warn!("Storage health check failed: {}", e);
            // Storage failures are considered degraded rather than unhealthy
            // since the service can still function in fallback mode
            "degraded".to_string()
        }
    }
}

/// Check PDF generation service health
async fn check_pdf_service_health(state: &AppState) -> String {
    debug!("Checking PDF service health");
    
    // Test PDF service by attempting to create a minimal PDF
    let test_html = r#"
        <!DOCTYPE html>
        <html>
        <head><title>Health Check</title></head>
        <body><p>Health check test</p></body>
        </html>
    "#;
    
    // Use a short timeout for health check
    let timeout = Duration::from_secs(5);
    
    match tokio::time::timeout(timeout, state.services.pdf.generate_pdf_from_html(test_html)).await {
        Ok(Ok(_)) => {
            debug!("PDF service health check passed");
            "healthy".to_string()
        },
        Ok(Err(e)) => {
            error!("PDF service health check failed: {}", e);
            "unhealthy".to_string()
        },
        Err(_) => {
            error!("PDF service health check timed out");
            "unhealthy".to_string()
        }
    }
}

/// Determine overall service status based on individual component health
pub fn determine_overall_status(database: &str, storage: &str, pdf: &str) -> String {
    // Database is critical - if it's unhealthy, the whole service is unhealthy
    if database == "unhealthy" {
        return "unhealthy".to_string();
    }
    
    // PDF service is critical for core functionality
    if pdf == "unhealthy" {
        return "unhealthy".to_string();
    }
    
    // If storage is degraded but other services are healthy, service is degraded
    if storage == "degraded" {
        return "degraded".to_string();
    }
    
    // If all services are healthy
    if database == "healthy" && storage == "healthy" && pdf == "healthy" {
        return "healthy".to_string();
    }
    
    // Default to degraded for any other combination
    "degraded".to_string()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_determine_overall_status() {
        // All healthy
        assert_eq!(
            determine_overall_status("healthy", "healthy", "healthy"),
            "healthy"
        );
        
        // Database unhealthy - critical failure
        assert_eq!(
            determine_overall_status("unhealthy", "healthy", "healthy"),
            "unhealthy"
        );
        
        // PDF service unhealthy - critical failure
        assert_eq!(
            determine_overall_status("healthy", "healthy", "unhealthy"),
            "unhealthy"
        );
        
        // Storage degraded - service degraded
        assert_eq!(
            determine_overall_status("healthy", "degraded", "healthy"),
            "degraded"
        );
        
        // Multiple issues
        assert_eq!(
            determine_overall_status("unhealthy", "degraded", "healthy"),
            "unhealthy"
        );
    }
}
