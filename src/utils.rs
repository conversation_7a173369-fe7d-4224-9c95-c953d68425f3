// Utility functions for the invoice generator API

use chrono::{DateTime, Utc};
use std::net::IpAddr;

/// Extract client IP address from request headers
pub fn extract_client_ip(headers: &axum::http::HeaderMap) -> Option<IpAddr> {
    // Try to get IP from X-Forwarded-For header first (for proxies)
    if let Some(forwarded) = headers.get("x-forwarded-for") {
        if let Ok(forwarded_str) = forwarded.to_str() {
            if let Some(ip_str) = forwarded_str.split(',').next() {
                if let Ok(ip) = ip_str.trim().parse::<IpAddr>() {
                    return Some(ip);
                }
            }
        }
    }
    
    // Try X-Real-IP header
    if let Some(real_ip) = headers.get("x-real-ip") {
        if let Ok(ip_str) = real_ip.to_str() {
            if let Ok(ip) = ip_str.parse::<IpAddr>() {
                return Some(ip);
            }
        }
    }
    
    None
}

/// Generate a unique filename for PDF storage
pub fn generate_pdf_filename(invoice_id: &str) -> String {
    format!("invoice_{}.pdf", invoice_id)
}

/// Calculate expiration date for PDF files
pub fn calculate_expiration_date(retention_days: i64) -> DateTime<Utc> {
    Utc::now() + chrono::Duration::days(retention_days)
}

/// Format currency amount for display
pub fn format_currency(amount: f64, currency: &str) -> String {
    match currency.to_uppercase().as_str() {
        "USD" => format!("${:.2}", amount),
        "EUR" => format!("€{:.2}", amount),
        "GBP" => format!("£{:.2}", amount),
        "AUD" => format!("A${:.2}", amount),
        _ => format!("{} {:.2}", currency, amount),
    }
}

/// Validate currency code (ISO 4217)
pub fn is_valid_currency_code(currency: &str) -> bool {
    // Common currency codes - in a real implementation, you'd want a comprehensive list
    const VALID_CURRENCIES: &[&str] = &[
        "USD", "EUR", "GBP", "AUD", "CAD", "JPY", "CHF", "CNY", "INR", "BRL",
        "KRW", "SGD", "HKD", "NOK", "SEK", "DKK", "PLN", "CZK", "HUF", "RON",
        "BGN", "HRK", "RUB", "TRY", "ZAR", "MXN", "NZD", "THB", "MYR", "IDR",
        "PHP", "VND",
    ];
    
    VALID_CURRENCIES.contains(&currency.to_uppercase().as_str())
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_generate_pdf_filename() {
        let filename = generate_pdf_filename("test-123");
        assert_eq!(filename, "invoice_test-123.pdf");
    }
    
    #[test]
    fn test_format_currency() {
        assert_eq!(format_currency(123.45, "USD"), "$123.45");
        assert_eq!(format_currency(123.45, "EUR"), "€123.45");
        assert_eq!(format_currency(123.45, "UNKNOWN"), "UNKNOWN 123.45");
    }
    
    #[test]
    fn test_is_valid_currency_code() {
        assert!(is_valid_currency_code("USD"));
        assert!(is_valid_currency_code("usd"));
        assert!(is_valid_currency_code("EUR"));
        assert!(!is_valid_currency_code("INVALID"));
    }
} 