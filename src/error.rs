use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
    J<PERSON>,
};
use serde_json::json;
use thiserror::Error;

/// Application-wide error type
#[derive(Error, Debug)]
pub enum AppError {
    #[error("Database error: {0}")]
    Database(#[from] sea_orm::DbErr),

    #[error("Storage error: {0}")]
    Storage(#[from] crate::services::storage::StorageError),

    #[error("Template error: {0}")]
    Template(#[from] crate::services::template::TemplateError),

    #[error("PDF generation error: {0}")]
    Pdf(#[from] crate::services::pdf::PdfError),

    #[error("Validation error: {0}")]
    Validation(String),

    #[error("Rate limit exceeded")]
    RateLimit,

    #[error("Configuration error: {0}")]
    Config(String),

    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),

    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),

    #[error("Internal server error: {0}")]
    Internal(String),

    #[error("Bad request: {0}")]
    BadRequest(String),

    #[error("Not found: {0}")]
    NotFound(String),

    #[error("Unauthorized")]
    Unauthorized,

    #[error("Forbidden")]
    Forbidden,
}

impl AppError {
    /// Get the appropriate HTTP status code for this error
    pub fn status_code(&self) -> StatusCode {
        match self {
            AppError::Database(_) => StatusCode::INTERNAL_SERVER_ERROR,
            AppError::Storage(_) => StatusCode::INTERNAL_SERVER_ERROR,
            AppError::Template(_) => StatusCode::INTERNAL_SERVER_ERROR,
            AppError::Pdf(_) => StatusCode::INTERNAL_SERVER_ERROR,
            AppError::Validation(_) => StatusCode::BAD_REQUEST,
            AppError::RateLimit => StatusCode::TOO_MANY_REQUESTS,
            AppError::Config(_) => StatusCode::INTERNAL_SERVER_ERROR,
            AppError::Io(_) => StatusCode::INTERNAL_SERVER_ERROR,
            AppError::Serialization(_) => StatusCode::BAD_REQUEST,
            AppError::Internal(_) => StatusCode::INTERNAL_SERVER_ERROR,
            AppError::BadRequest(_) => StatusCode::BAD_REQUEST,
            AppError::NotFound(_) => StatusCode::NOT_FOUND,
            AppError::Unauthorized => StatusCode::UNAUTHORIZED,
            AppError::Forbidden => StatusCode::FORBIDDEN,
        }
    }

    /// Get error code for API responses
    pub fn error_code(&self) -> &'static str {
        match self {
            AppError::Database(_) => "DATABASE_ERROR",
            AppError::Storage(_) => "STORAGE_ERROR",
            AppError::Template(_) => "TEMPLATE_ERROR",
            AppError::Pdf(_) => "PDF_ERROR",
            AppError::Validation(_) => "VALIDATION_ERROR",
            AppError::RateLimit => "RATE_LIMIT_EXCEEDED",
            AppError::Config(_) => "CONFIG_ERROR",
            AppError::Io(_) => "IO_ERROR",
            AppError::Serialization(_) => "SERIALIZATION_ERROR",
            AppError::Internal(_) => "INTERNAL_ERROR",
            AppError::BadRequest(_) => "BAD_REQUEST",
            AppError::NotFound(_) => "NOT_FOUND",
            AppError::Unauthorized => "UNAUTHORIZED",
            AppError::Forbidden => "FORBIDDEN",
        }
    }

    /// Check if error should be logged
    pub fn should_log(&self) -> bool {
        match self {
            AppError::Database(_) 
            | AppError::Storage(_) 
            | AppError::Template(_) 
            | AppError::Pdf(_) 
            | AppError::Config(_) 
            | AppError::Io(_) 
            | AppError::Internal(_) => true,
            _ => false,
        }
    }
}

impl IntoResponse for AppError {
    fn into_response(self) -> Response {
        let status = self.status_code();
        
        // Log internal errors
        if self.should_log() {
            tracing::error!("Application error: {}", self);
        }

        let body = Json(json!({
            "error": {
                "code": self.error_code(),
                "message": self.to_string(),
                "status": status.as_u16()
            }
        }));

        (status, body).into_response()
    }
}

/// Result type alias for application operations
pub type AppResult<T> = Result<T, AppError>;

/// Helper trait for converting errors to AppError
pub trait IntoAppError<T> {
    fn into_app_error(self) -> AppResult<T>;
}

impl<T, E> IntoAppError<T> for Result<T, E>
where
    E: Into<AppError>,
{
    fn into_app_error(self) -> AppResult<T> {
        self.map_err(Into::into)
    }
}
