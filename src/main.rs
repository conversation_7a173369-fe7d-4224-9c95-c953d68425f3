use axum::{
    routing::{get, post},
    Router,
    middleware::{from_fn, from_fn_with_state},
};
use std::net::SocketAddr;
use tower_http::trace::TraceLayer;
use tracing::{info, warn};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

mod config;
mod database;
mod error;
mod handlers;
mod middleware;
mod models;
mod repositories;
mod services;
mod state;
mod tasks;
mod utils;

use config::Config;
use state::AppState;
use middleware::{
    monitoring::{correlation_id_middleware, request_logging_middleware, initialize_metrics},
    rate_limit::rate_limit_middleware,
    security::{security_headers_middleware, request_size_middleware, request_timeout_middleware, create_cors_layer},
};
use error::AppResult;



#[tokio::main]
async fn main() -> AppResult<()> {
    // Load configuration first for monitoring settings
    let config = Config::load().unwrap_or_else(|e| {
        eprintln!("Failed to load configuration: {}", e);
        std::process::exit(1);
    });

    // Initialize tracing with structured logging based on config
    if config.monitoring.enable_tracing {
        let log_filter = format!("invoice_generator_api={},tower_http=info,axum=debug", config.monitoring.log_level);
        tracing_subscriber::registry()
            .with(
                tracing_subscriber::EnvFilter::try_from_default_env()
                    .unwrap_or_else(|_| log_filter.into()),
            )
            .with(
                tracing_subscriber::fmt::layer()
                    .with_target(true)
                    .with_thread_ids(true)
                    .with_file(true)
                    .with_line_number(true)
            )
            .init();
    }

    // Initialize metrics system if enabled
    if config.monitoring.enable_metrics {
        if let Err(e) = initialize_metrics() {
            warn!("Failed to initialize metrics system: {}", e);
            // Continue without metrics rather than failing
        }
    }

    // Configuration already loaded above for monitoring setup
    info!("Configuration loaded successfully");

    // Initialize application state
    let state = AppState::new(config.clone()).await?;
    info!("Application state initialized successfully");

    // Perform storage health check
    if let Err(e) = state.services.storage.health_check().await {
        warn!("Storage health check failed: {}. Continuing without R2 storage.", e);
    } else {
        info!("Storage health check passed");
    }

    // Start background tasks (cleanup schedulers)
    if let Err(e) = state.start_background_tasks() {
        warn!("Failed to start background tasks: {}. Continuing without scheduled cleanup.", e);
    } else {
        info!("Background tasks started successfully");
    }

    // Build application router
    let app = create_app(&config).await?;

    // Add state and rate limiting middleware
    let app_with_state = app
        .layer(from_fn_with_state(
            state.clone(),
            rate_limit_middleware,
        ))
        .with_state(state);

    // Start server
    let addr = SocketAddr::from(([0, 0, 0, 0], config.port));
    info!("Starting server on {}", addr);

    let listener = tokio::net::TcpListener::bind(addr).await?;
    axum::serve(
        listener,
        app_with_state.into_make_service_with_connect_info::<SocketAddr>()
    ).await?;

    Ok(())
}

async fn create_app(config: &Config) -> AppResult<Router<AppState>> {
    let mut app: Router<AppState> = Router::new()
        // Health check endpoint
        .route("/health", get(handlers::health::health_check));

    // Add metrics endpoint if enabled
    if config.monitoring.metrics_endpoint_enabled {
        app = app.route("/metrics", get(|| async {
            // For now, return a simple metrics placeholder
            // In a real implementation, you'd use the metrics handle stored during initialization
            let metrics_data = "# Metrics endpoint\n# TODO: Implement proper metrics export\n";
            ([("content-type", "text/plain; charset=utf-8")], metrics_data)
        }));
    }

    // Add API routes
    app = app
        .route("/api/v1/invoices", post(handlers::invoices::generate_invoice))
        .route("/api/v1/templates", get(handlers::templates::list_templates));

    // Add middleware (order matters - applied in reverse)
    app = app.layer(TraceLayer::new_for_http());

    // Add monitoring middleware if enabled
    if config.monitoring.enable_request_logging {
        app = app.layer(from_fn(request_logging_middleware));
    }

    if config.monitoring.enable_correlation_ids {
        app = app.layer(from_fn(correlation_id_middleware));
    }

    // Add remaining middleware
    app = app
        .layer(create_cors_layer())
        .layer(from_fn(security_headers_middleware))
        .layer(from_fn(request_size_middleware))
        .layer(from_fn(request_timeout_middleware));

    Ok(app)
}




