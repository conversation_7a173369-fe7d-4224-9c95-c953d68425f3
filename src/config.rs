use serde::{Deserialize, Serialize};
use std::env;

#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub struct Config {
    pub port: u16,
    pub database_url: String,
    pub r2_endpoint: String,
    pub r2_access_key: String,
    pub r2_secret_key: String,
    pub r2_bucket_name: String,
    pub r2_region: String,
    pub rate_limit: RateLimitConfig,
    pub pdf_retention_days: i64,
    pub max_request_size: usize,
    pub wkhtmltopdf_path: Option<String>,
    pub database_max_connections: u32,
    pub database_min_connections: u32,
    pub database_connect_timeout: u64,
    pub security: SecurityConfig,
    pub monitoring: MonitoringConfig,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct RateLimitConfig {
    pub max_requests: i32,
    pub window_duration_minutes: i64,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SecurityConfig {
    pub enable_hsts: bool,
    pub enable_csp: bool,
    pub request_timeout_seconds: u64,
    pub allowed_origins: Vec<String>,
    pub enable_security_headers: bool,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct MonitoringConfig {
    pub enable_metrics: bool,
    pub enable_tracing: bool,
    pub log_level: String,
    pub enable_correlation_ids: bool,
    pub enable_request_logging: bool,
    pub metrics_endpoint_enabled: bool,
}

impl Config {
    pub fn load() -> Result<Self, Box<dyn std::error::Error>> {
        // Load environment variables from .env file if present
        dotenv::dotenv().ok();

        let config = Config {
            port: env::var("PORT")
                .unwrap_or_else(|_| "3000".to_string())
                .parse()
                .unwrap_or(3000),
            database_url: env::var("DATABASE_URL")
                .unwrap_or_else(|_| "postgresql://localhost/invoice_generator".to_string()),
            r2_endpoint: env::var("R2_ENDPOINT")
                .unwrap_or_else(|_| "https://your-account-id.r2.cloudflarestorage.com".to_string()),
            r2_access_key: env::var("R2_ACCESS_KEY")
                .unwrap_or_else(|_| "your-access-key".to_string()),
            r2_secret_key: env::var("R2_SECRET_KEY")
                .unwrap_or_else(|_| "your-secret-key".to_string()),
            r2_bucket_name: env::var("R2_BUCKET_NAME")
                .unwrap_or_else(|_| "invoice-pdfs".to_string()),
            r2_region: env::var("R2_REGION")
                .unwrap_or_else(|_| "auto".to_string()),
            rate_limit: RateLimitConfig {
                max_requests: env::var("RATE_LIMIT_REQUESTS")
                    .unwrap_or_else(|_| "5".to_string())
                    .parse()
                    .unwrap_or(5),
                window_duration_minutes: env::var("RATE_LIMIT_WINDOW_SECONDS")
                    .unwrap_or_else(|_| "60".to_string())
                    .parse::<u64>()
                    .unwrap_or(60) as i64 / 60, // Convert seconds to minutes
            },
            pdf_retention_days: env::var("PDF_RETENTION_DAYS")
                .unwrap_or_else(|_| "7".to_string())
                .parse()
                .unwrap_or(7),
            max_request_size: env::var("MAX_REQUEST_SIZE")
                .unwrap_or_else(|_| "1048576".to_string()) // 1MB default
                .parse()
                .unwrap_or(1048576),
            wkhtmltopdf_path: env::var("WKHTMLTOPDF_PATH").ok(),
            database_max_connections: env::var("DATABASE_MAX_CONNECTIONS")
                .unwrap_or_else(|_| "100".to_string())
                .parse()
                .unwrap_or(100),
            database_min_connections: env::var("DATABASE_MIN_CONNECTIONS")
                .unwrap_or_else(|_| "5".to_string())
                .parse()
                .unwrap_or(5),
            database_connect_timeout: env::var("DATABASE_CONNECT_TIMEOUT")
                .unwrap_or_else(|_| "8".to_string())
                .parse()
                .unwrap_or(8),
            security: SecurityConfig {
                enable_hsts: env::var("ENABLE_HSTS")
                    .unwrap_or_else(|_| "true".to_string())
                    .parse()
                    .unwrap_or(true),
                enable_csp: env::var("ENABLE_CSP")
                    .unwrap_or_else(|_| "true".to_string())
                    .parse()
                    .unwrap_or(true),
                request_timeout_seconds: env::var("REQUEST_TIMEOUT_SECONDS")
                    .unwrap_or_else(|_| "30".to_string())
                    .parse()
                    .unwrap_or(30),
                allowed_origins: env::var("ALLOWED_ORIGINS")
                    .unwrap_or_else(|_| "*".to_string())
                    .split(',')
                    .map(|s| s.trim().to_string())
                    .collect(),
                enable_security_headers: env::var("ENABLE_SECURITY_HEADERS")
                    .unwrap_or_else(|_| "true".to_string())
                    .parse()
                    .unwrap_or(true),
            },
            monitoring: MonitoringConfig {
                enable_metrics: env::var("ENABLE_METRICS")
                    .unwrap_or_else(|_| "true".to_string())
                    .parse()
                    .unwrap_or(true),
                enable_tracing: env::var("ENABLE_TRACING")
                    .unwrap_or_else(|_| "true".to_string())
                    .parse()
                    .unwrap_or(true),
                log_level: env::var("LOG_LEVEL")
                    .unwrap_or_else(|_| "info".to_string()),
                enable_correlation_ids: env::var("ENABLE_CORRELATION_IDS")
                    .unwrap_or_else(|_| "true".to_string())
                    .parse()
                    .unwrap_or(true),
                enable_request_logging: env::var("ENABLE_REQUEST_LOGGING")
                    .unwrap_or_else(|_| "true".to_string())
                    .parse()
                    .unwrap_or(true),
                metrics_endpoint_enabled: env::var("METRICS_ENDPOINT_ENABLED")
                    .unwrap_or_else(|_| "true".to_string())
                    .parse()
                    .unwrap_or(true),
            },
        };

        Ok(config)
    }
} 