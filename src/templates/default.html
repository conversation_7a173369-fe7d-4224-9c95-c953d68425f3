<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="author" content="Invoice Generator API">
  <meta name="description" content="Professional Invoice Template">
  <title>Invoice</title>

  <!-- Print and PDF optimized styles -->
  <style>
    /* CSS Custom Properties for maintainability and theming */
    :root {
      /* Color Palette - Default Professional Theme */
      --color-primary: #2c3e50;
      --color-primary-light: #34495e;
      --color-secondary: #3498db;
      --color-accent: #e74c3c;
      --color-success: #27ae60;
      --color-warning: #f39c12;

      /* Neutral Colors */
      --color-text-primary: #333333;
      --color-text-secondary: #6c757d;
      --color-text-muted: #adb5bd;
      --color-text-light: #dee2e6;
      --color-background: #ffffff;
      --color-background-light: #f8f9fa;
      --color-background-neutral: #e9ecef;
      --color-border: #dee2e6;
      --color-border-light: #e9ecef;
      --color-white: #ffffff;

      /* Alternating row colors */
      --color-row-even: #ffffff;
      --color-row-odd: #f8f9fa;
      --color-row-hover: #e3f2fd;

      /* Watermark and pattern colors */
      --color-watermark: rgba(44, 62, 80, 0.03);
      --color-pattern: rgba(52, 73, 94, 0.02);

      /* Typography Scale */
      --font-family-primary: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      --font-family-mono: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace;
      --font-size-xs: 0.75rem;
      --font-size-sm: 0.875rem;
      --font-size-base: 1rem;
      --font-size-lg: 1.125rem;
      --font-size-xl: 1.25rem;
      --font-size-2xl: 1.5rem;
      --font-size-3xl: 1.875rem;
      --font-size-4xl: 2.25rem;
      --font-weight-light: 300;
      --font-weight-normal: 400;
      --font-weight-medium: 500;
      --font-weight-semibold: 600;
      --font-weight-bold: 700;

      /* Line Heights */
      --line-height-tight: 1.25;
      --line-height-base: 1.5;
      --line-height-relaxed: 1.625;

      /* Spacing Scale */
      --spacing-xs: 0.25rem;
      --spacing-sm: 0.5rem;
      --spacing-md: 0.75rem;
      --spacing-lg: 1rem;
      --spacing-xl: 1.5rem;
      --spacing-2xl: 2rem;
      --spacing-3xl: 3rem;
      --spacing-4xl: 4rem;

      /* Border Radius */
      --border-radius-sm: 0.25rem;
      --border-radius-md: 0.375rem;
      --border-radius-lg: 0.5rem;
      --border-radius-xl: 0.75rem;

      /* Shadows */
      --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

      /* Transitions */
      --transition-fast: 150ms ease-in-out;
      --transition-base: 200ms ease-in-out;
      --transition-slow: 300ms ease-in-out;
    }

    /* Alternative Color Themes */

    /* Modern Blue Theme */
    [data-theme="modern-blue"] {
      --color-primary: #1e40af;
      --color-primary-light: #3b82f6;
      --color-secondary: #06b6d4;
      --color-accent: #8b5cf6;
      --color-success: #10b981;
      --color-warning: #f59e0b;
      --color-watermark: rgba(30, 64, 175, 0.03);
      --color-pattern: rgba(59, 130, 246, 0.02);
    }

    /* Corporate Green Theme */
    [data-theme="corporate-green"] {
      --color-primary: #065f46;
      --color-primary-light: #047857;
      --color-secondary: #059669;
      --color-accent: #dc2626;
      --color-success: #16a34a;
      --color-warning: #ca8a04;
      --color-watermark: rgba(6, 95, 70, 0.03);
      --color-pattern: rgba(4, 120, 87, 0.02);
    }

    /* Elegant Purple Theme */
    [data-theme="elegant-purple"] {
      --color-primary: #581c87;
      --color-primary-light: #7c3aed;
      --color-secondary: #a855f7;
      --color-accent: #ec4899;
      --color-success: #22c55e;
      --color-warning: #eab308;
      --color-watermark: rgba(88, 28, 135, 0.03);
      --color-pattern: rgba(124, 58, 237, 0.02);
    }

    /* Monochrome Theme */
    [data-theme="monochrome"] {
      --color-primary: #000000;
      --color-primary-light: #374151;
      --color-secondary: #6b7280;
      --color-accent: #ef4444;
      --color-success: #22c55e;
      --color-warning: #f59e0b;
      --color-watermark: rgba(0, 0, 0, 0.03);
      --color-pattern: rgba(55, 65, 81, 0.02);
    }

    /* Reset and base styles */
    *,
    *::before,
    *::after {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: var(--font-family-primary);
      line-height: var(--line-height-base);
      color: var(--color-text-primary);
      background-color: var(--color-background);
      max-width: 210mm;
      /* A4 width */
      margin: 0 auto;
      padding: 20mm;
      font-size: var(--font-size-base);
      position: relative;
    }

    /* Subtle background pattern */
    body::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image:
        radial-gradient(circle at 25% 25%, var(--color-pattern) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, var(--color-pattern) 0%, transparent 50%);
      background-size: 60px 60px;
      background-position: 0 0, 30px 30px;
      pointer-events: none;
      z-index: -2;
    }

    /* Watermark */
    .invoice-container::before {
      content: attr(data-watermark);
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) rotate(-45deg);
      font-size: 8rem;
      font-weight: var(--font-weight-bold);
      color: var(--color-watermark);
      pointer-events: none;
      z-index: -1;
      white-space: nowrap;
      user-select: none;
    }

    /* Focus styles for accessibility */
    *:focus {
      outline: 2px solid var(--color-secondary);
      outline-offset: 2px;
    }

    /* Skip link for accessibility */
    .skip-link {
      position: absolute;
      top: -40px;
      left: 6px;
      background: var(--color-primary);
      color: white;
      padding: 8px;
      text-decoration: none;
      border-radius: var(--border-radius-sm);
      z-index: 100;
    }

    .skip-link:focus {
      top: 6px;
    }

    .invoice-container {
      width: 100%;
      min-height: 100vh;
      position: relative;
    }

    /* Enhanced screen reader support */
    .sr-only {
      position: absolute;
      width: 1px;
      height: 1px;
      padding: 0;
      margin: -1px;
      overflow: hidden;
      clip: rect(0, 0, 0, 0);
      white-space: nowrap;
      border: 0;
    }

    /* Header section */
    .invoice-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: var(--spacing-4xl);
      padding-bottom: var(--spacing-xl);
      border-bottom: 2px solid var(--color-border);
      transition: border-color var(--transition-base);
    }

    .logo-section {
      flex: 1;
    }

    .logo-placeholder {
      width: 150px;
      height: 60px;
      border: 2px dashed var(--color-border);
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--color-text-light);
      font-size: var(--font-size-xs);
      margin-bottom: var(--spacing-md);
      border-radius: var(--border-radius-sm);
      transition: border-color var(--transition-base);
    }

    .logo-placeholder:hover {
      border-color: var(--color-secondary);
    }

    .invoice-title {
      flex: 1;
      text-align: right;
    }

    .invoice-title h1 {
      font-size: var(--font-size-4xl);
      font-weight: var(--font-weight-normal);
      color: var(--color-primary);
      margin-bottom: var(--spacing-md);
      letter-spacing: -0.025em;
    }

    .invoice-number {
      font-size: var(--font-size-lg);
      color: var(--color-text-secondary);
      font-weight: var(--font-weight-medium);
    }

    /* Contact sections */
    .contact-section {
      display: flex;
      justify-content: space-between;
      margin-bottom: var(--spacing-4xl);
      gap: var(--spacing-4xl);
    }

    .from-section,
    .billed-to-section {
      flex: 1;
      padding: var(--spacing-lg);
      background-color: var(--color-background-light);
      border-radius: var(--border-radius-md);
      border: 1px solid var(--color-border-light);
      transition: box-shadow var(--transition-base);
      position: relative;
    }

    .from-section:hover,
    .billed-to-section:hover {
      box-shadow: var(--shadow-sm);
    }

    .section-title {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--color-primary);
      margin-bottom: var(--spacing-lg);
      text-transform: uppercase;
      letter-spacing: 0.05em;
      position: relative;
    }

    .section-title::after {
      content: '';
      position: absolute;
      bottom: -4px;
      left: 0;
      width: 30px;
      height: 2px;
      background-color: var(--color-secondary);
      border-radius: 1px;
    }

    .contact-info {
      line-height: var(--line-height-relaxed);
    }

    .contact-name {
      font-weight: var(--font-weight-semibold);
      font-size: var(--font-size-lg);
      margin-bottom: var(--spacing-sm);
      color: var(--color-text-primary);
    }

    .contact-address {
      color: var(--color-text-secondary);
      margin-bottom: var(--spacing-md);
    }

    .contact-details {
      color: var(--color-text-muted);
    }

    /* Invoice details section */
    .invoice-details {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: var(--spacing-xl);
      margin-bottom: var(--spacing-4xl);
      padding: var(--spacing-xl);
      background: linear-gradient(135deg, var(--color-background-light) 0%, #f1f3f4 100%);
      border-radius: var(--border-radius-lg);
      border: 1px solid var(--color-border-light);
      box-shadow: var(--shadow-sm);
    }

    .detail-item {
      text-align: center;
      padding: var(--spacing-md);
      border-radius: var(--border-radius-md);
      background-color: var(--color-white);
      transition: transform var(--transition-fast), box-shadow var(--transition-fast);
    }

    .detail-item:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
    }

    .detail-label {
      font-size: var(--font-size-sm);
      color: var(--color-text-muted);
      text-transform: uppercase;
      letter-spacing: 0.05em;
      margin-bottom: var(--spacing-sm);
      font-weight: var(--font-weight-medium);
    }

    .detail-value {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--color-primary);
    }

    /* Items table with enhanced styling */
    .items-section {
      margin-bottom: var(--spacing-4xl);
    }

    .items-table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: var(--spacing-xl);
      border-radius: var(--border-radius-md);
      overflow: hidden;
      box-shadow: var(--shadow-sm);
    }

    .items-table th {
      background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
      color: var(--color-white);
      padding: var(--spacing-lg) var(--spacing-md);
      text-align: left;
      font-weight: var(--font-weight-semibold);
      font-size: var(--font-size-sm);
      text-transform: uppercase;
      letter-spacing: 0.05em;
      border: none;
      position: sticky;
      top: 0;
      z-index: 10;
    }

    .items-table th:nth-child(2),
    .items-table th:nth-child(3),
    .items-table th:nth-child(4) {
      text-align: right;
    }

    .items-table td {
      padding: var(--spacing-lg) var(--spacing-md);
      border-bottom: 1px solid var(--color-border-light);
      vertical-align: top;
      transition: background-color var(--transition-fast);
    }

    /* Alternating row colors */
    .items-table tbody tr:nth-child(even) {
      background-color: var(--color-row-even);
    }

    .items-table tbody tr:nth-child(odd) {
      background-color: var(--color-row-odd);
    }

    .items-table tbody tr:hover {
      background-color: var(--color-row-hover);
      transform: scale(1.001);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .items-table td:nth-child(2),
    .items-table td:nth-child(3),
    .items-table td:nth-child(4) {
      text-align: right;
      font-variant-numeric: tabular-nums;
    }

    .item-description {
      font-weight: var(--font-weight-semibold);
      color: var(--color-text-primary);
      margin-bottom: var(--spacing-xs);
      line-height: var(--line-height-tight);
    }

    .item-details {
      font-size: var(--font-size-sm);
      color: var(--color-text-muted);
      line-height: var(--line-height-base);
    }

    /* Mobile table styles */
    .items-table-mobile {
      display: none;
    }

    /* Calculations section */
    .calculations-section {
      display: flex;
      justify-content: flex-end;
      margin-bottom: var(--spacing-4xl);
    }

    .calculations-table {
      border-collapse: collapse;
      box-shadow: var(--shadow-sm);
      border-radius: var(--border-radius-md);
      overflow: hidden;
      min-width: 300px;
      background-color: var(--color-white);
    }

    .calculations-table tr:last-child {
      border-bottom: none;
    }

    .calculations-table td {
      padding: var(--spacing-md) var(--spacing-lg);
    }

    .calculation-value,
    .calc-value {
      text-align: right;
      font-weight: var(--font-weight-semibold);
      color: var(--color-primary);
      font-variant-numeric: tabular-nums;
    }

    .calculation-label,
    .calc-label {
      font-weight: var(--font-weight-medium);
      color: var(--color-text-secondary);
    }

    .total-row {
      background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
      color: var(--color-white);
    }

    .total-row .calculation-label,
    .total-row .calculation-value,
    .total-row .calc-label,
    .total-row .calc-value {
      color: var(--color-white);
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-bold);
    }

    /* Conditional sections styling */
    .conditional-section {
      opacity: 0;
      max-height: 0;
      overflow: hidden;
      transition: opacity var(--transition-base), max-height var(--transition-base);
    }

    .conditional-section.visible {
      opacity: 1;
      max-height: 1000px;
    }

    /* Enhanced discount section */
    .discount-highlight {
      background: linear-gradient(90deg, rgba(231, 76, 60, 0.1) 0%, transparent 100%);
      border-left: 3px solid var(--color-accent);
      padding-left: var(--spacing-sm);
    }

    /* Enhanced tax sections */
    .tax-highlight {
      background: linear-gradient(90deg, rgba(52, 152, 219, 0.1) 0%, transparent 100%);
      border-left: 3px solid var(--color-secondary);
      padding-left: var(--spacing-sm);
    }

    /* Advanced animations and polish */
    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(20px);
      }

      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes slideInRight {
      from {
        opacity: 0;
        transform: translateX(30px);
      }

      to {
        opacity: 1;
        transform: translateX(0);
      }
    }

    @keyframes pulseGlow {

      0%,
      100% {
        box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
      }

      50% {
        box-shadow: 0 0 20px rgba(52, 152, 219, 0.6);
      }
    }

    /* Apply animations */
    .invoice-header {
      animation: fadeInUp 0.6s ease-out;
    }

    .contact-section {
      animation: fadeInUp 0.6s ease-out 0.1s both;
    }

    .invoice-details {
      animation: fadeInUp 0.6s ease-out 0.2s both;
    }

    .items-section {
      animation: fadeInUp 0.6s ease-out 0.3s both;
    }

    .calculations-section {
      animation: slideInRight 0.6s ease-out 0.4s both;
    }

    .invoice-footer {
      animation: fadeInUp 0.6s ease-out 0.5s both;
    }

    /* Focus glow effect */
    .total-row:focus-within {
      animation: pulseGlow 2s infinite;
    }

    /* Enhanced hover states */
    .from-section:hover .section-title::after,
    .billed-to-section:hover .section-title::after {
      width: 50px;
      transition: width var(--transition-base);
    }

    /* Printing state adjustments */
    body.printing {
      animation: none !important;
    }

    body.printing * {
      animation: none !important;
      transition: none !important;
    }

    /* High contrast mode support */
    @media (prefers-contrast: high) {
      :root {
        --color-border: #000000;
        --color-text-secondary: #000000;
        --color-background-light: #ffffff;
      }
    }

    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {

      *,
      *::before,
      *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
      }
    }

    /* Dark mode support (for screen viewing) */
    @media (prefers-color-scheme: dark) {
      :root {
        --color-background: #1a1a1a;
        --color-background-light: #2d2d2d;
        --color-text-primary: #ffffff;
        --color-text-secondary: #cccccc;
        --color-border: #444444;
      }
    }

    /* Enhanced loading state */
    body {
      opacity: 0;
      transition: opacity 0.3s ease-in-out;
    }

    body.loaded {
      opacity: 1;
    }

    /* Footer section */
    .invoice-footer {
      margin-top: var(--spacing-4xl);
      padding-top: var(--spacing-2xl);
      border-top: 1px solid var(--color-border);
    }

    .payment-terms,
    .invoice-notes {
      margin-bottom: var(--spacing-xl);
      padding: var(--spacing-lg);
      background-color: var(--color-background-light);
      border-radius: var(--border-radius-md);
      border-left: 4px solid var(--color-secondary);
    }

    .terms-title {
      font-weight: var(--font-weight-semibold);
      color: var(--color-primary);
      margin-bottom: var(--spacing-md);
      font-size: var(--font-size-lg);
    }

    .terms-content {
      color: var(--color-text-muted);
      line-height: var(--line-height-base);
    }

    .footer-text {
      text-align: center;
      font-size: var(--font-size-sm);
      color: var(--color-text-light);
      margin-top: var(--spacing-2xl);
      padding: var(--spacing-lg);
      font-style: italic;
    }

    /* Print styles */
    @media print {
      :root {
        --color-background-light: #ffffff;
        --color-background-neutral: #f8f9fa;
        --color-border: #dee2e6;
        --color-border-light: #e9ecef;
      }

      /* Page setup for PDF generation */
      @page {
        size: A4;
        margin: 20mm 15mm 25mm 15mm;
        /* top, right, bottom, left */

        @top-left {
          content: "Invoice #" attr(data-invoice-number);
          font-size: 10pt;
          color: #6c757d;
        }

        @top-right {
          content: "Page " counter(page) " of " counter(pages);
          font-size: 10pt;
          color: #6c757d;
        }

        @bottom-center {
          content: "Generated on " attr(data-generation-date);
          font-size: 9pt;
          color: #6c757d;
        }
      }

      /* First page specific margins */
      @page :first {
        margin-top: 15mm;

        @top-left {
          content: none;
        }

        @top-right {
          content: none;
        }
      }

      /* Page counter setup */
      html {
        counter-reset: page;
      }

      body {
        margin: 0;
        padding: 0;
        font-size: 11pt;
        line-height: 1.4;
        color: #333333;
        background: white;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
        font-family: "Helvetica Neue", Arial, sans-serif;
      }

      .invoice-container {
        min-height: auto;
        width: 100%;
        max-width: none;
        margin: 0;
        padding: 0;
      }

      /* Header optimizations */
      .invoice-header {
        page-break-after: avoid;
        page-break-inside: avoid;
        margin-bottom: 8mm;
        padding-bottom: 4mm;
        border-bottom: 1.5pt solid var(--color-primary);
      }

      .logo-section {
        width: 45%;
      }

      .invoice-title {
        width: 45%;
      }

      .invoice-title h1 {
        font-size: 24pt;
        font-weight: 400;
        margin-bottom: 2mm;
      }

      .invoice-number {
        font-size: 12pt;
        font-weight: 500;
      }

      /* Contact sections optimization */
      .contact-section {
        page-break-after: avoid;
        page-break-inside: avoid;
        margin-bottom: 8mm;
        gap: 6mm;
      }

      .from-section,
      .billed-to-section {
        box-shadow: none;
        border: 1pt solid var(--color-border);
        background-color: #fafbfc;
        padding: 4mm;
        border-radius: 2mm;
      }

      .section-title {
        font-size: 11pt;
        font-weight: 600;
        margin-bottom: 3mm;
      }

      .section-title::after {
        width: 15mm;
        height: 1pt;
      }

      .contact-name {
        font-size: 12pt;
        font-weight: 600;
        margin-bottom: 2mm;
      }

      .contact-address,
      .contact-details {
        font-size: 10pt;
        line-height: 1.3;
      }

      /* Invoice details optimization */
      .invoice-details {
        page-break-after: avoid;
        page-break-inside: avoid;
        margin-bottom: 8mm;
        padding: 4mm;
        background: #f8f9fa !important;
        border: 1pt solid var(--color-border);
        border-radius: 2mm;
        box-shadow: none;
        grid-template-columns: repeat(auto-fit, minmax(35mm, 1fr));
        gap: 3mm;
      }

      .detail-item {
        background-color: white;
        border: 1pt solid var(--color-border-light);
        border-radius: 2mm;
        padding: 3mm;
        text-align: center;
        box-shadow: none;
      }

      .detail-label {
        font-size: 9pt;
        font-weight: 500;
        margin-bottom: 1mm;
      }

      .detail-value {
        font-size: 11pt;
        font-weight: 600;
      }

      /* Items table optimization */
      .items-section {
        margin-bottom: 6mm;
      }

      .items-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 4mm;
        border-radius: 0;
        box-shadow: none;
        page-break-inside: auto;
      }

      .items-table th {
        background: var(--color-primary) !important;
        color: white !important;
        padding: 3mm 2mm;
        font-size: 10pt;
        font-weight: 600;
        border: none;
        page-break-after: avoid;
      }

      .items-table td {
        padding: 2.5mm 2mm;
        border-bottom: 0.5pt solid var(--color-border);
        font-size: 10pt;
        vertical-align: top;
        page-break-inside: avoid;
      }

      .items-table tbody tr {
        page-break-inside: avoid;
        page-break-after: auto;
      }

      /* Avoid breaking table rows across pages */
      .items-table tbody tr:nth-last-child(-n+3) {
        page-break-inside: avoid;
        page-break-after: avoid;
      }

      .item-description {
        font-weight: 500;
        margin-bottom: 1mm;
      }

      .item-details {
        font-size: 9pt;
        color: #6c757d;
        line-height: 1.2;
      }

      /* Hide mobile table in print */
      .items-table-mobile {
        display: none !important;
      }

      /* Calculations section optimization */
      .calculations-section {
        page-break-inside: avoid;
        margin-bottom: 8mm;
        justify-content: flex-end;
      }

      .calculations-table {
        border-collapse: collapse;
        box-shadow: none;
        border: 1pt solid var(--color-border);
        border-radius: 2mm;
        overflow: hidden;
        min-width: 50mm;
        max-width: 70mm;
      }

      .calculations-table td {
        padding: 2.5mm 3mm;
        border-bottom: 0.5pt solid var(--color-border-light);
        font-size: 10pt;
      }

      .calc-label {
        font-weight: 500;
        width: 60%;
      }

      .calc-value {
        text-align: right;
        font-weight: 500;
        width: 40%;
      }

      .total-row {
        background: var(--color-primary) !important;
        color: white !important;
        font-weight: 600;
        font-size: 11pt;
      }

      .total-row td {
        border-bottom: none;
        padding: 3mm;
      }

      /* Footer optimization */
      .invoice-footer {
        page-break-inside: avoid;
        margin-top: 8mm;
        padding-top: 4mm;
        border-top: 1pt solid var(--color-border);
        font-size: 9pt;
        line-height: 1.3;
        color: #6c757d;
      }

      .payment-terms {
        margin-bottom: 3mm;
      }

      .footer-notes {
        font-style: italic;
      }

      /* Page break helpers */
      .page-break {
        page-break-before: always;
      }

      .page-break-after {
        page-break-after: always;
      }

      .no-break {
        page-break-inside: avoid;
      }

      .avoid-break-before {
        page-break-before: avoid;
      }

      .avoid-break-after {
        page-break-after: avoid;
      }

      /* Large invoice handling - force page breaks for long item lists */
      .items-table tbody tr:nth-child(15n) {
        page-break-after: always;
      }

      /* Repeated table headers for multi-page tables */
      .items-table thead {
        display: table-header-group;
      }

      .items-table tfoot {
        display: table-footer-group;
      }

      /* Remove all hover effects and transitions */
      * {
        transition: none !important;
        animation: none !important;
      }

      .logo-placeholder:hover,
      .detail-item:hover,
      .items-table tbody tr:hover,
      .from-section:hover,
      .billed-to-section:hover {
        transform: none !important;
        background-color: inherit !important;
        box-shadow: none !important;
      }

      /* Ensure proper text rendering */
      .invoice-title h1,
      .section-title,
      .contact-name,
      .calc-label,
      .calc-value {
        text-rendering: optimizeSpeed;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }

      /* Hide elements that shouldn't appear in PDF */
      .interactive-only {
        display: none !important;
      }

      /* Ensure proper color contrast for PDF */
      .text-muted {
        color: #6c757d !important;
      }

      .text-primary {
        color: var(--color-primary) !important;
      }

      /* Force backgrounds for better PDF rendering */
      .items-table th,
      .total-row {
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
      }
    }

    /* Responsive design */
    @media (max-width: 768px) {
      body {
        padding: var(--spacing-lg);
      }

      .invoice-header {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-xl);
      }

      .invoice-title {
        text-align: center;
        margin-top: 0;
      }

      .invoice-title h1 {
        font-size: var(--font-size-3xl);
      }

      .contact-section {
        flex-direction: column;
        gap: var(--spacing-2xl);
      }

      .invoice-details {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
      }

      /* Mobile table - hide desktop table and show mobile cards */
      .items-table {
        display: none;
      }

      .items-table-mobile {
        display: block;
      }

      .item-card {
        background-color: var(--color-background-light);
        border: 1px solid var(--color-border);
        border-radius: var(--border-radius-md);
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-md);
        box-shadow: var(--shadow-sm);
      }

      .item-card:hover {
        box-shadow: var(--shadow-md);
        transform: translateY(-1px);
        transition: all var(--transition-fast);
      }

      .item-card-header {
        border-bottom: 1px solid var(--color-border);
        padding-bottom: var(--spacing-md);
        margin-bottom: var(--spacing-md);
      }

      .item-card-body {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-md);
      }

      .item-card-field {
        display: flex;
        flex-direction: column;
      }

      .item-card-label {
        font-size: var(--font-size-xs);
        color: var(--color-text-muted);
        text-transform: uppercase;
        letter-spacing: 0.05em;
        margin-bottom: var(--spacing-xs);
        font-weight: var(--font-weight-medium);
      }

      .item-card-value {
        font-weight: var(--font-weight-semibold);
        color: var(--color-text-primary);
      }

      .calculations-section {
        justify-content: center;
      }

      .calculations-table {
        min-width: 250px;
      }
    }

    @media (max-width: 480px) {
      .invoice-details {
        padding: var(--spacing-md);
      }

      .detail-item {
        padding: var(--spacing-sm);
      }

      .item-card-body {
        grid-template-columns: 1fr;
      }

      .calculations-table {
        min-width: 200px;
      }
    }

    /* Dark mode support */
    @media (prefers-color-scheme: dark) {
      :root {
        --color-text-primary: #f8f9fa;
        --color-text-secondary: #e9ecef;
        --color-text-muted: #ced4da;
        --color-text-light: #adb5bd;
        --color-background: #212529;
        --color-background-light: #343a40;
        --color-background-neutral: #495057;
        --color-border: #495057;
        --color-border-light: #6c757d;
      }
    }

    /* High contrast mode support */
    @media (prefers-contrast: high) {
      :root {
        --color-primary: #000000;
        --color-text-primary: #000000;
        --color-text-secondary: #333333;
        --color-border: #000000;
      }
    }
  </style>
</head>

<body data-invoice-number="{{invoice_number}}" data-generation-date="{{now}}" data-theme="modern-blue" {{#if
  discount}}data-has-discount="true" {{/if}} {{#if tax}}data-has-tax="true" {{/if}} {{#if gst}}data-has-gst="true"
  {{/if}}>
  <!-- Skip link for accessibility -->
  <a href="#main-content" class="skip-link">Skip to main content</a>

  <div class="invoice-container" data-watermark="{{#if watermark}}{{watermark}}{{else}}SAMPLE{{/if}}" id="main-content"
    role="main">
    <!-- Header Section -->
    <header class="invoice-header">
      <div class="logo-section">
        <div class="logo-placeholder" role="img" aria-label="Company logo placeholder">
          LOGO
        </div>
        <div class="company-info">
          <div class="contact-name">{{from.name}}</div>
          {{#if from.address}}
          <div class="contact-address">
            {{from.address.street}}<br>
            {{from.address.city}}, {{from.address.state}} {{from.address.post_code}}<br>
            {{from.address.country}}
          </div>
          {{/if}}
          {{#if from.email}}<div>Email: {{from.email}}</div>{{/if}}
          {{#if from.phone}}<div>Phone: {{from.phone}}</div>{{/if}}
          {{#if from.abn}}<div>ABN: {{from.abn}}</div>{{/if}}
        </div>
      </div>
      <div class="invoice-title">
        <h1>INVOICE</h1>
        <div class="invoice-number">#{{invoice_number}}</div>
      </div>
    </header>

    <!-- Contact Information -->
    <section class="contact-section">
      <div class="from-section">
        <h2 class="section-title">From</h2>
        <div class="contact-info">
          <div class="contact-name">{{from.name}}</div>
          {{#if from.address}}
          <div class="contact-address">
            {{from.address.street}}<br>
            {{from.address.city}}, {{from.address.state}} {{from.address.post_code}}<br>
            {{from.address.country}}
          </div>
          {{/if}}
          <div class="contact-details">
            {{#if from.email}}<div>{{from.email}}</div>{{/if}}
            {{#if from.phone}}<div>{{from.phone}}</div>{{/if}}
            {{#if from.abn}}<div>ABN: {{from.abn}}</div>{{/if}}
          </div>
        </div>
      </div>

      <div class="billed-to-section">
        <h2 class="section-title">Billed To</h2>
        <div class="contact-info">
          <div class="contact-name">{{billed_to.name}}</div>
          {{#if billed_to.address}}
          <div class="contact-address">
            {{billed_to.address.street}}<br>
            {{billed_to.address.city}}, {{billed_to.address.state}} {{billed_to.address.post_code}}<br>
            {{billed_to.address.country}}
          </div>
          {{/if}}
          <div class="contact-details">
            {{#if billed_to.email}}<div>{{billed_to.email}}</div>{{/if}}
            {{#if billed_to.phone}}<div>{{billed_to.phone}}</div>{{/if}}
            {{#if billed_to.abn}}<div>ABN: {{billed_to.abn}}</div>{{/if}}
          </div>
        </div>
      </div>
    </section>

    <!-- Invoice Details -->
    <section class="invoice-details">
      <div class="detail-item">
        <div class="detail-label">Invoice Date</div>
        <div class="detail-value">{{format_date invoice_date}}</div>
      </div>
      {{#if due_date}}
      <div class="detail-item">
        <div class="detail-label">Due Date</div>
        <div class="detail-value">{{format_date due_date}}</div>
      </div>
      {{/if}}
      <div class="detail-item">
        <div class="detail-label">Currency</div>
        <div class="detail-value">{{currency}}</div>
      </div>
      {{#if payment_terms}}
      <div class="detail-item">
        <div class="detail-label">Payment Terms</div>
        <div class="detail-value">{{payment_terms}}</div>
      </div>
      {{/if}}
    </section>

    <!-- Items Table - Desktop -->
    <section class="items-section no-break">
      <table class="items-table" role="table" aria-label="Invoice items">
        <thead>
          <tr>
            <th scope="col">Description</th>
            <th scope="col" class="text-center">Quantity</th>
            <th scope="col" class="text-right">Unit Price</th>
            <th scope="col" class="text-right">Total</th>
          </tr>
        </thead>
        <tbody>
          {{#each items}}
          <tr>
            <td>
              <div class="item-description">{{name}}</div>
              {{#if description}}
              <div class="item-details">{{description}}</div>
              {{/if}}
            </td>
            <td class="text-center">{{quantity}}</td>
            <td class="text-right">{{format_currency ../currency price}}</td>
            <td class="text-right">{{format_currency ../currency total}}</td>
          </tr>
          {{/each}}
        </tbody>
      </table>

      <!-- Items Cards - Mobile -->
      <div class="items-table-mobile" role="list" aria-label="Invoice items">
        {{#each items}}
        <div class="item-card" role="listitem">
          <div class="item-card-header">
            <div class="item-description">{{name}}</div>
            {{#if description}}
            <div class="item-details">{{description}}</div>
            {{/if}}
          </div>
          <div class="item-card-body">
            <div class="item-card-field">
              <div class="item-card-label">Quantity</div>
              <div class="item-card-value">{{quantity}}</div>
            </div>
            <div class="item-card-field">
              <div class="item-card-label">Unit Price</div>
              <div class="item-card-value">{{format_currency ../currency price}}</div>
            </div>
            <div class="item-card-field">
              <div class="item-card-label">Total</div>
              <div class="item-card-value">{{format_currency ../currency total}}</div>
            </div>
          </div>
        </div>
        {{/each}}
      </div>
    </section>

    <!-- Calculations -->
    <section class="calculations-section avoid-break-before">
      <table class="calculations-table" role="table" aria-label="Invoice calculations">
        <tr>
          <td class="calc-label">Subtotal</td>
          <td class="calc-value">{{format_currency currency subtotal}}</td>
        </tr>
        {{#if discount}}
        <tr class="conditional-section discount-highlight visible">
          <td class="calc-label">
            Discount
            {{#if discount.percentage}}({{discount.percentage}}%){{/if}}
          </td>
          <td class="calc-value">-{{format_currency currency discount_amount}}</td>
        </tr>
        {{/if}}
        {{#if shipping_fee}}
        <tr>
          <td class="calc-label">Shipping</td>
          <td class="calc-value">{{format_currency currency shipping_fee}}</td>
        </tr>
        {{/if}}
        {{#if tax}}
        <tr class="conditional-section tax-highlight visible">
          <td class="calc-label">Tax ({{tax}}%)</td>
          <td class="calc-value">{{format_currency currency tax_amount}}</td>
        </tr>
        {{/if}}
        {{#if gst}}
        <tr class="conditional-section tax-highlight visible">
          <td class="calc-label">GST ({{gst}}%)</td>
          <td class="calc-value">{{format_currency currency gst_amount}}</td>
        </tr>
        {{/if}}
        <tr class="total-row">
          <td class="calc-label">TOTAL</td>
          <td class="calc-value">{{format_currency currency total}}</td>
        </tr>
      </table>
    </section>

    <!-- Footer -->
    <footer class="invoice-footer">
      {{#if payment_terms}}
      <div class="payment-terms">
        <div class="terms-title">Payment Terms</div>
        <div class="terms-content">{{payment_terms}}</div>
      </div>
      {{/if}}

      {{#if notes}}
      <div class="invoice-notes">
        <div class="terms-title">Notes</div>
        <div class="terms-content">{{notes}}</div>
      </div>
      {{/if}}

      <div class="footer-text">
        Thank you for your business!
      </div>
    </footer>
  </div>

  <!-- Advanced JavaScript Features -->
  <script>
    // Theme management
    const applyTheme = (theme) => {
      document.body.setAttribute('data-theme', theme);
      localStorage.setItem('invoice-theme', theme);
    };

    // Load saved theme
    const savedTheme = localStorage.getItem('invoice-theme');
    if (savedTheme) {
      applyTheme(savedTheme);
    }

    // Conditional section visibility
    const toggleConditionalSections = () => {
      // Show discount section if discount exists
      const hasDiscount = document.querySelector('[data-has-discount="true"]');
      if (hasDiscount) {
        const discountSections = document.querySelectorAll('.discount-highlight');
        discountSections.forEach(section => section.classList.add('visible'));
      }

      // Show tax sections if tax exists
      const hasTax = document.querySelector('[data-has-tax="true"]');
      if (hasTax) {
        const taxSections = document.querySelectorAll('.tax-highlight');
        taxSections.forEach(section => section.classList.add('visible'));
      }
    };

    // Enhanced accessibility
    const enhanceAccessibility = () => {
      // Add keyboard navigation for interactive elements
      const interactiveElements = document.querySelectorAll('.detail-item, .item-card');
      interactiveElements.forEach((element, index) => {
        element.setAttribute('tabindex', '0');
        element.setAttribute('role', 'button');
        element.setAttribute('aria-label', `Invoice detail item ${index + 1}`);
      });

      // Add focus indicators
      const addFocusIndicators = () => {
        const style = document.createElement('style');
        style.textContent = `
          .detail-item:focus,
          .item-card:focus {
            outline: 2px solid var(--color-secondary);
            outline-offset: 2px;
            border-radius: var(--border-radius-md);
          }
        `;
        document.head.appendChild(style);
      };

      addFocusIndicators();
    };

    // Print optimization
    const optimizeForPrint = () => {
      // Add print-specific classes
      window.addEventListener('beforeprint', () => {
        document.body.classList.add('printing');

        // Ensure all conditional sections are visible for print
        const conditionalSections = document.querySelectorAll('.conditional-section');
        conditionalSections.forEach(section => {
          section.style.opacity = '1';
          section.style.maxHeight = 'none';
        });
      });

      window.addEventListener('afterprint', () => {
        document.body.classList.remove('printing');

        // Restore conditional section visibility
        toggleConditionalSections();
      });
    };

    // Enhanced table interactions
    const enhanceTableInteractions = () => {
      const tableRows = document.querySelectorAll('.items-table tbody tr');

      tableRows.forEach((row, index) => {
        row.setAttribute('data-row-index', index);

        // Add subtle animation on hover
        row.addEventListener('mouseenter', () => {
          row.style.transform = 'translateX(2px)';
        });

        row.addEventListener('mouseleave', () => {
          row.style.transform = 'translateX(0)';
        });
      });
    };

    // Currency formatting enhancement
    const enhanceCurrencyDisplay = () => {
      const currencyElements = document.querySelectorAll('.calc-value, .calculation-value');

      currencyElements.forEach(element => {
        const value = element.textContent;
        if (value && value.includes('.')) {
          element.innerHTML = value.replace(/(\.\d{2})/, '<span style="font-size: 0.9em;">$1</span>');
        }
      });
    };

    // Watermark management
    const manageWatermark = () => {
      const container = document.querySelector('.invoice-container');
      const watermarkText = container.getAttribute('data-watermark');

      if (watermarkText && watermarkText.trim()) {
        container.style.position = 'relative';
      }
    };

    // Initialize all features
    document.addEventListener('DOMContentLoaded', () => {
      toggleConditionalSections();
      enhanceAccessibility();
      optimizeForPrint();
      enhanceTableInteractions();
      enhanceCurrencyDisplay();
      manageWatermark();

      // Add loading animation
      document.body.style.opacity = '0';
      document.body.style.transition = 'opacity 0.3s ease-in-out';

      setTimeout(() => {
        document.body.style.opacity = '1';
      }, 100);
    });

    // Responsive table management
    const manageResponsiveTable = () => {
      const checkTableVisibility = () => {
        const desktopTable = document.querySelector('.items-table');
        const mobileTable = document.querySelector('.items-table-mobile');

        if (window.innerWidth < 768) {
          desktopTable.style.display = 'none';
          mobileTable.style.display = 'block';
        } else {
          desktopTable.style.display = 'table';
          mobileTable.style.display = 'none';
        }
      };

      checkTableVisibility();
      window.addEventListener('resize', checkTableVisibility);
    };

    // Initialize responsive features
    document.addEventListener('DOMContentLoaded', manageResponsiveTable);
  </script>
</body>

</html>