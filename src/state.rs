use std::sync::Arc;

use crate::{
    config::Config,
    database::Database,
    services::{
        rate_limit::RateLimitService,
        storage::StorageService,
        template::TemplateService,
        validation::ValidationService,
        pdf::PdfService,
    },
    tasks::scheduler::TaskScheduler,
    error::AppResult,
};

/// Application state shared across all handlers
#[derive(Clone)]
pub struct AppState {
    pub config: Arc<Config>,
    pub database: Database,
    pub services: Services,
    pub scheduler: Arc<TaskScheduler>,
}

/// Collection of all application services
#[derive(Clone)]
pub struct Services {
    pub storage: StorageService,
    pub rate_limit: Arc<RateLimitService>,
    pub template: TemplateService,
    pub validation: ValidationService,
    pub pdf: PdfService,
}

impl AppState {
    /// Create a new application state
    pub async fn new(config: Config) -> AppResult<Self> {
        let config = Arc::new(config);
        
        // Initialize database
        let database = Database::new(
            &config.database_url,
            config.database_max_connections,
            config.database_min_connections,
            config.database_connect_timeout,
        ).await?;

        // Run migrations
        database.migrate().await?;

        // Initialize services
        let services = Services::new(&config, &database).await?;

        // Initialize task scheduler
        let scheduler = Arc::new(TaskScheduler::new(
            Arc::clone(&config),
            &database,
            services.storage.clone(),
        ));

        Ok(Self {
            config,
            database,
            services,
            scheduler,
        })
    }

    /// Start background tasks (schedulers)
    pub fn start_background_tasks(&self) -> AppResult<()> {
        self.scheduler.start()
    }

    /// Get database connection
    pub fn db(&self) -> &sea_orm::DatabaseConnection {
        self.database.get_connection()
    }
}

impl Services {
    /// Initialize all services
    pub async fn new(config: &Config, database: &Database) -> AppResult<Self> {
        let storage = StorageService::new(config).await?;
        
        let rate_limit = Arc::new(RateLimitService::new(
            database.get_connection().clone(),
            config.rate_limit.max_requests,
            config.rate_limit.window_duration_minutes,
        ));

        let template = TemplateService::new()?;
        let validation = ValidationService::new();
        let pdf = PdfService::new();

        Ok(Self {
            storage,
            rate_limit,
            template,
            validation,
            pdf,
        })
    }
}
