use axum::{
    extract::Request,
    http::{header, HeaderMap, HeaderValue, Method, StatusCode},
    middleware::Next,
    response::{IntoResponse, Response},
};
use std::time::Duration;
use tokio::time::timeout;
use tower_http::cors::{Any, CorsLayer};
use tracing::{debug, warn};

use crate::error::AppError;

/// Security configuration
#[derive(Debug, Clone)]
pub struct SecurityConfig {
    pub max_request_size: usize,
    pub request_timeout: Duration,
    pub enable_hsts: bool,
    pub enable_csp: bool,
    pub allowed_origins: Vec<String>,
}

impl Default for SecurityConfig {
    fn default() -> Self {
        Self {
            max_request_size: 1024 * 1024, // 1MB
            request_timeout: Duration::from_secs(30),
            enable_hsts: true,
            enable_csp: true,
            allowed_origins: vec!["*".to_string()], // For development - should be restricted in production
        }
    }
}

/// Security headers middleware
pub async fn security_headers_middleware(
    req: Request,
    next: Next,
) -> Result<Response, AppError> {
    debug!("Applying security headers middleware");

    // Process the request
    let mut response = next.run(req).await;

    // Add security headers
    let headers = response.headers_mut();
    
    // Strict Transport Security (HSTS)
    headers.insert(
        header::STRICT_TRANSPORT_SECURITY,
        HeaderValue::from_static("max-age=31536000; includeSubDomains; preload"),
    );

    // Content Security Policy
    headers.insert(
        header::CONTENT_SECURITY_POLICY,
        HeaderValue::from_static(
            "default-src 'self'; \
            script-src 'self' 'unsafe-inline'; \
            style-src 'self' 'unsafe-inline'; \
            img-src 'self' data: https:; \
            font-src 'self'; \
            connect-src 'self'; \
            frame-ancestors 'none'; \
            base-uri 'self'; \
            form-action 'self'"
        ),
    );

    // X-Content-Type-Options
    headers.insert(
        header::X_CONTENT_TYPE_OPTIONS,
        HeaderValue::from_static("nosniff"),
    );

    // X-Frame-Options
    headers.insert(
        header::X_FRAME_OPTIONS,
        HeaderValue::from_static("DENY"),
    );

    // X-XSS-Protection
    headers.insert(
        "x-xss-protection",
        HeaderValue::from_static("1; mode=block"),
    );

    // Referrer Policy
    headers.insert(
        "referrer-policy",
        HeaderValue::from_static("strict-origin-when-cross-origin"),
    );

    // Permissions Policy
    headers.insert(
        "permissions-policy",
        HeaderValue::from_static(
            "camera=(), microphone=(), geolocation=(), payment=(), usb=(), \
             magnetometer=(), gyroscope=(), accelerometer=()"
        ),
    );

    // Remove server information
    headers.remove(header::SERVER);

    Ok(response)
}

/// Request size validation middleware
pub async fn request_size_middleware(
    req: Request,
    next: Next,
) -> Result<Response, AppError> {
    // Check content-length header
    if let Some(content_length) = req.headers().get(header::CONTENT_LENGTH) {
        if let Ok(length_str) = content_length.to_str() {
            if let Ok(length) = length_str.parse::<usize>() {
                const MAX_SIZE: usize = 10 * 1024 * 1024; // 10MB
                if length > MAX_SIZE {
                    warn!("Request size {} exceeds maximum allowed size {}", length, MAX_SIZE);
                    return Err(AppError::BadRequest(
                        "Request body too large".to_string()
                    ));
                }
            }
        }
    }

    Ok(next.run(req).await)
}

/// Request timeout middleware
pub async fn request_timeout_middleware(
    req: Request,
    next: Next,
) -> Result<Response, AppError> {
    const TIMEOUT_DURATION: Duration = Duration::from_secs(30);

    match timeout(TIMEOUT_DURATION, next.run(req)).await {
        Ok(response) => Ok(response),
        Err(_) => {
            warn!("Request timed out after {} seconds", TIMEOUT_DURATION.as_secs());
            Err(AppError::Internal("Request timeout".to_string()))
        }
    }
}

/// Input sanitization utilities
pub mod sanitization {
    use regex::Regex;
    use std::sync::OnceLock;

    /// Sanitize HTML content to prevent XSS
    pub fn sanitize_html(input: &str) -> String {
        static SCRIPT_REGEX: OnceLock<Regex> = OnceLock::new();
        static HTML_REGEX: OnceLock<Regex> = OnceLock::new();
        static MALFORMED_REGEX: OnceLock<Regex> = OnceLock::new();

        let script_regex = SCRIPT_REGEX.get_or_init(|| {
            Regex::new(r"(?i)<+script[^>]*>.*?</script>").unwrap()
        });

        let html_regex = HTML_REGEX.get_or_init(|| {
            Regex::new(r"<[^>]*>").unwrap()
        });

        let malformed_regex = MALFORMED_REGEX.get_or_init(|| {
            Regex::new(r"<+").unwrap()
        });

        // First remove script tags and their content completely (including malformed ones)
        let without_scripts = script_regex.replace_all(input, "");

        // Then remove all other HTML tags
        let without_tags = html_regex.replace_all(&without_scripts, "");

        // Remove any remaining malformed < characters
        let without_malformed = malformed_regex.replace_all(&without_tags, "");

        // Then decode HTML entities and remove any remaining dangerous content
        without_malformed
            .replace("&lt;", "<")
            .replace("&gt;", ">")
            .replace("&amp;", "&")
            .replace("&quot;", "\"")
            .replace("&#x27;", "'")
            .replace("&#x2F;", "/")
            .replace("&#x60;", "`")
            .replace("&#x3D;", "=")
            // Remove any remaining script-like content
            .replace("javascript:", "")
            .replace("vbscript:", "")
            .replace("onload=", "")
            .replace("onerror=", "")
            .replace("onclick=", "")
            .replace("onmouseover=", "")
            .to_string()
    }

    /// Sanitize SQL-like patterns (basic protection)
    pub fn sanitize_sql_patterns(input: &str) -> Result<String, String> {
        // Check for common SQL injection patterns
        let dangerous_patterns = [
            "union", "select", "insert", "update", "delete", "drop",
            "create", "alter", "exec", "execute", "--", "/*", "*/",
            ";", "'", "\"", "&"
        ];

        let input_lower = input.to_lowercase();
        for pattern in &dangerous_patterns {
            if input_lower.contains(pattern) {
                return Err("Input contains potentially dangerous SQL patterns".to_string());
            }
        }

        Ok(input.to_string())
    }

    /// Validate and sanitize file paths
    pub fn sanitize_file_path(path: &str) -> Result<String, String> {
        // Prevent directory traversal
        if path.contains("..") || path.contains("//") {
            return Err("Invalid file path: directory traversal detected".to_string());
        }

        // Remove null bytes
        if path.contains('\0') {
            return Err("Invalid file path: null byte detected".to_string());
        }

        // Basic path validation
        static PATH_REGEX: OnceLock<Regex> = OnceLock::new();
        let regex = PATH_REGEX.get_or_init(|| {
            Regex::new(r"^[a-zA-Z0-9._/-]+$").unwrap()
        });

        if !regex.is_match(path) {
            return Err("Invalid file path: contains illegal characters".to_string());
        }

        Ok(path.to_string())
    }

    /// Sanitize user input for logging (prevent log injection)
    pub fn sanitize_for_logging(input: &str) -> String {
        input
            .replace('\n', "\\n")
            .replace('\r', "\\r")
            .replace('\t', "\\t")
            .chars()
            .filter(|c| c.is_ascii() && !c.is_control())
            .collect()
    }
}

/// Create a properly configured CORS layer
pub fn create_cors_layer() -> CorsLayer {
    CorsLayer::new()
        .allow_origin(Any) // In production, this should be restricted to specific origins
        .allow_methods([Method::GET, Method::POST, Method::OPTIONS])
        .allow_headers([
            header::CONTENT_TYPE,
            header::AUTHORIZATION,
            header::ACCEPT,
            header::USER_AGENT,
        ])
        .max_age(Duration::from_secs(3600)) // 1 hour
}

/// Rate limiting headers helper
pub fn add_rate_limit_headers(headers: &mut HeaderMap, remaining: i32, reset_time: i64) {
    if let Ok(remaining_value) = HeaderValue::from_str(&remaining.to_string()) {
        headers.insert("x-ratelimit-remaining", remaining_value);
    }
    
    if let Ok(reset_value) = HeaderValue::from_str(&reset_time.to_string()) {
        headers.insert("x-ratelimit-reset", reset_value);
    }
    
    headers.insert(
        "x-ratelimit-limit",
        HeaderValue::from_static("5"), // Should come from config
    );
}

#[cfg(test)]
mod tests {
    use super::sanitization::*;

    #[test]
    fn test_html_sanitization() {
        assert_eq!(sanitize_html("<script>alert('xss')</script>"), "");
        assert_eq!(sanitize_html("Hello <b>world</b>!"), "Hello world!");
        assert_eq!(sanitize_html("Normal text"), "Normal text");
    }

    #[test]
    fn test_sql_pattern_detection() {
        assert!(sanitize_sql_patterns("'; DROP TABLE users; --").is_err());
        assert!(sanitize_sql_patterns("UNION SELECT * FROM passwords").is_err());
        assert!(sanitize_sql_patterns("normal text").is_ok());
    }

    #[test]
    fn test_file_path_sanitization() {
        assert!(sanitize_file_path("../../../etc/passwd").is_err());
        assert!(sanitize_file_path("normal/path.txt").is_ok());
        assert!(sanitize_file_path("path\0with\0nulls").is_err());
    }

    #[test]
    fn test_logging_sanitization() {
        assert_eq!(sanitize_for_logging("test\nwith\nnewlines"), "test\\nwith\\nnewlines");
        assert_eq!(sanitize_for_logging("normal text"), "normal text");
    }
}
