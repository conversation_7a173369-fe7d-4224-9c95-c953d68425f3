#[cfg(test)]
mod tests {
    use crate::middleware::security::*;
    use axum::{
        body::Body,
        http::{Request, StatusCode},
        middleware::from_fn,
        routing::get,
        Router,
    };
    use tower::util::ServiceExt;

    async fn test_handler() -> &'static str {
        "test response"
    }

    #[tokio::test]
    async fn test_security_headers_middleware() {
        let app = Router::new()
            .route("/test", get(test_handler))
            .layer(from_fn(security_headers_middleware));

        let request = Request::builder()
            .uri("/test")
            .body(Body::empty())
            .unwrap();

        let response = app.oneshot(request).await.unwrap();

        // Check that security headers are present
        let headers = response.headers();
        
        assert!(headers.contains_key("strict-transport-security"));
        assert!(headers.contains_key("content-security-policy"));
        assert!(headers.contains_key("x-content-type-options"));
        assert!(headers.contains_key("x-frame-options"));
        assert!(headers.contains_key("x-xss-protection"));
        assert!(headers.contains_key("referrer-policy"));
        assert!(headers.contains_key("permissions-policy"));
        
        // Check that server header is removed
        assert!(!headers.contains_key("server"));
    }

    #[tokio::test]
    async fn test_request_size_middleware_valid_size() {
        let app = Router::new()
            .route("/test", get(test_handler))
            .layer(from_fn(request_size_middleware));

        let request = Request::builder()
            .uri("/test")
            .header("content-length", "1000") // 1KB - should be fine
            .body(Body::empty())
            .unwrap();

        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::OK);
    }

    #[tokio::test]
    async fn test_request_size_middleware_oversized() {
        let app = Router::new()
            .route("/test", get(test_handler))
            .layer(from_fn(request_size_middleware));

        let request = Request::builder()
            .uri("/test")
            .header("content-length", "20971520") // 20MB - should be rejected
            .body(Body::empty())
            .unwrap();

        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::BAD_REQUEST);
    }

    #[tokio::test]
    async fn test_cors_layer_configuration() {
        let cors_layer = create_cors_layer();
        // This test mainly ensures the CORS layer can be created without panicking
        // More detailed CORS testing would require integration tests
        assert!(true); // Placeholder assertion
    }

    mod sanitization_tests {
        use crate::middleware::security::sanitization::*;

        #[test]
        fn test_html_sanitization() {
            assert_eq!(sanitize_html("<script>alert('xss')</script>"), "");
            assert_eq!(sanitize_html("Hello <b>world</b>!"), "Hello world!");
            assert_eq!(sanitize_html("Normal text"), "Normal text");
            assert_eq!(sanitize_html("<img src=x onerror=alert(1)>"), "");
        }

        #[test]
        fn test_sql_pattern_detection() {
            // Should detect dangerous patterns
            assert!(sanitize_sql_patterns("'; DROP TABLE users; --").is_err());
            assert!(sanitize_sql_patterns("UNION SELECT * FROM passwords").is_err());
            assert!(sanitize_sql_patterns("admin'--").is_err());
            assert!(sanitize_sql_patterns("1' OR '1'='1").is_err());
            
            // Should allow normal text
            assert!(sanitize_sql_patterns("normal text").is_ok());
            assert!(sanitize_sql_patterns("<EMAIL>").is_ok());
            assert!(sanitize_sql_patterns("Product description").is_ok());
        }

        #[test]
        fn test_file_path_sanitization() {
            // Should reject directory traversal
            assert!(sanitize_file_path("../../../etc/passwd").is_err());
            assert!(sanitize_file_path("..\\windows\\system32").is_err());
            assert!(sanitize_file_path("normal/path.txt").is_ok());
            
            // Should reject null bytes
            assert!(sanitize_file_path("path\0with\0nulls").is_err());
            
            // Should reject invalid characters
            assert!(sanitize_file_path("path<>with|invalid*chars").is_err());
            
            // Should allow valid paths
            assert!(sanitize_file_path("documents/invoice.pdf").is_ok());
            assert!(sanitize_file_path("templates/default.html").is_ok());
        }

        #[test]
        fn test_logging_sanitization() {
            assert_eq!(sanitize_for_logging("test\nwith\nnewlines"), "test\\nwith\\nnewlines");
            assert_eq!(sanitize_for_logging("test\rwith\rcarriage"), "test\\rwith\\rcarriage");
            assert_eq!(sanitize_for_logging("test\twith\ttabs"), "test\\twith\\ttabs");
            assert_eq!(sanitize_for_logging("normal text"), "normal text");
            
            // Should filter control characters
            let input_with_control = "test\x00\x01\x02control";
            let sanitized = sanitize_for_logging(input_with_control);
            assert!(!sanitized.contains('\x00'));
            assert!(!sanitized.contains('\x01'));
            assert!(!sanitized.contains('\x02'));
        }

        #[test]
        fn test_edge_cases() {
            // Empty strings
            assert_eq!(sanitize_html(""), "");
            assert!(sanitize_sql_patterns("").is_ok());
            assert_eq!(sanitize_for_logging(""), "");
            
            // Very long strings
            let long_string = "a".repeat(10000);
            assert_eq!(sanitize_html(&long_string), long_string);
            assert!(sanitize_sql_patterns(&long_string).is_ok());
            
            // Unicode characters
            assert_eq!(sanitize_html("Hello 世界"), "Hello 世界");
            assert!(sanitize_sql_patterns("用户名").is_ok());
        }

        #[test]
        fn test_case_insensitive_sql_detection() {
            // Should detect patterns regardless of case
            assert!(sanitize_sql_patterns("UNION select").is_err());
            assert!(sanitize_sql_patterns("Union Select").is_err());
            assert!(sanitize_sql_patterns("uNiOn SeLeCt").is_err());
            assert!(sanitize_sql_patterns("DROP table").is_err());
            assert!(sanitize_sql_patterns("drop TABLE").is_err());
        }

        #[test]
        fn test_complex_html_sanitization() {
            // Nested tags
            assert_eq!(sanitize_html("<div><script>alert(1)</script></div>"), "");
            
            // Mixed content
            assert_eq!(
                sanitize_html("Hello <b>world</b> and <script>alert('xss')</script> everyone!"),
                "Hello world and  everyone!"
            );
            
            // Self-closing tags
            assert_eq!(sanitize_html("Image: <img src='test.jpg' />"), "Image: ");
            
            // Malformed HTML
            assert_eq!(sanitize_html("<<script>alert(1)</script>"), "");
        }
    }
}
