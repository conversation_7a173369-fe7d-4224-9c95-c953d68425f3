use axum::{
    extract::{ConnectInfo, MatchedPath, Request},
    http::{HeaderValue, Method},
    middleware::Next,
    response::Response,
};
use metrics::{histogram, increment_counter, increment_gauge, decrement_gauge};
use std::{
    net::SocketAddr,
    time::{Duration, Instant},
};
use tracing::{debug, error, info, warn};
use uuid::Uuid;

use crate::error::AppError;

/// HTTP header name for correlation ID
pub const CORRELATION_ID_HEADER: &str = "x-correlation-id";

/// Request context with correlation ID and timing information
#[derive(Debug, Clone)]
pub struct RequestContext {
    pub correlation_id: String,
    pub start_time: Instant,
    pub method: String,
    pub path: String,
    pub client_ip: String,
}

impl RequestContext {
    pub fn new(method: Method, path: String, client_ip: String) -> Self {
        Self {
            correlation_id: Uuid::new_v4().to_string(),
            start_time: Instant::now(),
            method: method.to_string(),
            path,
            client_ip,
        }
    }

    pub fn duration(&self) -> Duration {
        self.start_time.elapsed()
    }
}

/// Correlation ID middleware that adds unique request IDs for tracing
pub async fn correlation_id_middleware(
    mut req: Request,
    next: Next,
) -> Result<Response, AppError> {
    // Extract or generate correlation ID
    let correlation_id = req
        .headers()
        .get(CORRELATION_ID_HEADER)
        .and_then(|h| h.to_str().ok())
        .map(|s| s.to_string())
        .unwrap_or_else(|| Uuid::new_v4().to_string());

    // Add correlation ID to request headers for downstream services
    req.headers_mut().insert(
        CORRELATION_ID_HEADER,
        HeaderValue::from_str(&correlation_id)
            .map_err(|e| AppError::Internal(format!("Invalid correlation ID: {}", e)))?,
    );

    // Create a tracing span with correlation ID
    let span = tracing::info_span!(
        "request",
        correlation_id = %correlation_id,
        method = %req.method(),
        uri = %req.uri(),
    );

    // Clone correlation ID for response headers
    let response_correlation_id = correlation_id.clone();

    // Process request within the span
    let response = span.in_scope(|| async move {
        debug!("Processing request with correlation ID: {}", correlation_id);
        next.run(req).await
    }).await;

    // Add correlation ID to response headers
    let mut response = response;
    response.headers_mut().insert(
        CORRELATION_ID_HEADER,
        HeaderValue::from_str(&response_correlation_id)
            .map_err(|e| AppError::Internal(format!("Invalid correlation ID: {}", e)))?,
    );

    Ok(response)
}

/// Request/Response logging middleware with metrics collection
pub async fn request_logging_middleware(
    ConnectInfo(addr): ConnectInfo<SocketAddr>,
    matched_path: Option<MatchedPath>,
    req: Request,
    next: Next,
) -> Result<Response, AppError> {
    let start_time = Instant::now();
    let method = req.method().clone();
    let uri = req.uri().clone();
    let path = matched_path
        .map(|mp| mp.as_str().to_string())
        .unwrap_or_else(|| uri.path().to_string());
    // Extract client IP from headers or connection info
    let client_ip = crate::utils::extract_client_ip(req.headers())
        .map(|ip| ip.to_string())
        .unwrap_or_else(|| addr.ip().to_string());

    // Extract correlation ID from headers
    let correlation_id = req
        .headers()
        .get(CORRELATION_ID_HEADER)
        .and_then(|h| h.to_str().ok())
        .map(|s| s.to_string())
        .unwrap_or_else(|| "unknown".to_string());

    // Log request start
    info!(
        correlation_id = %correlation_id,
        method = %method,
        path = %path,
        client_ip = %client_ip,
        "Request started"
    );

    // Increment request counter
    increment_counter!("http_requests_total", "method" => method.to_string(), "path" => path.clone());
    increment_gauge!("http_requests_active", 1.0);

    // Process request
    let response = next.run(req).await;
    let duration = start_time.elapsed();
    let status = response.status();

    // Record metrics
    histogram!("http_request_duration_seconds", duration.as_secs_f64(), "method" => method.to_string(), "path" => path.clone(), "status" => status.as_u16().to_string());
    increment_counter!("http_responses_total", "method" => method.to_string(), "path" => path.clone(), "status" => status.as_u16().to_string());
    decrement_gauge!("http_requests_active", 1.0);

    // Log response with appropriate level
    if status.is_server_error() {
        error!(
            correlation_id = correlation_id,
            method = %method,
            path = %path,
            status = %status.as_u16(),
            duration_ms = duration.as_millis(),
            client_ip = %client_ip,
            "Request completed with server error"
        );
    } else if status.is_client_error() {
        warn!(
            correlation_id = correlation_id,
            method = %method,
            path = %path,
            status = %status.as_u16(),
            duration_ms = duration.as_millis(),
            client_ip = %client_ip,
            "Request completed with client error"
        );
    } else {
        info!(
            correlation_id = correlation_id,
            method = %method,
            path = %path,
            status = %status.as_u16(),
            duration_ms = duration.as_millis(),
            client_ip = %client_ip,
            "Request completed successfully"
        );
    }

    Ok(response)
}

/// Metrics collection for business operations
pub struct BusinessMetrics;

impl BusinessMetrics {
    /// Record PDF generation metrics
    pub fn record_pdf_generation(duration: Duration, success: bool, file_size: Option<usize>) {
        let status = if success { "success" } else { "failure" };
        
        histogram!("pdf_generation_duration_seconds", duration.as_secs_f64(), "status" => status);
        increment_counter!("pdf_generation_total", "status" => status);
        
        if success {
            increment_counter!("pdf_generation_success_total");
            if let Some(size) = file_size {
                histogram!("pdf_file_size_bytes", size as f64);
            }
        } else {
            increment_counter!("pdf_generation_failure_total");
        }
    }

    /// Record storage operation metrics
    pub fn record_storage_operation(operation: &str, duration: Duration, success: bool, file_size: Option<usize>) {
        let status = if success { "success" } else { "failure" };
        let operation_owned = operation.to_string();

        histogram!("storage_operation_duration_seconds", duration.as_secs_f64(), "operation" => operation_owned.clone(), "status" => status);
        increment_counter!("storage_operations_total", "operation" => operation_owned, "status" => status);

        if success && operation == "upload" {
            if let Some(size) = file_size {
                histogram!("storage_upload_size_bytes", size as f64);
            }
        }
    }

    /// Record database operation metrics
    pub fn record_database_operation(operation: &str, table: &str, duration: Duration, success: bool) {
        let status = if success { "success" } else { "failure" };
        let operation_owned = operation.to_string();
        let table_owned = table.to_string();

        histogram!("database_operation_duration_seconds", duration.as_secs_f64(), "operation" => operation_owned.clone(), "table" => table_owned.clone(), "status" => status);
        increment_counter!("database_operations_total", "operation" => operation_owned, "table" => table_owned, "status" => status);
    }

    /// Record rate limiting metrics
    pub fn record_rate_limit_check(allowed: bool, ip: &str) {
        let status = if allowed { "allowed" } else { "blocked" };
        increment_counter!("rate_limit_checks_total", "status" => status);
        
        if !allowed {
            increment_counter!("rate_limit_blocks_total");
            warn!(ip = ip, "Rate limit exceeded");
        }
    }

    /// Record template rendering metrics
    pub fn record_template_rendering(template_id: &str, duration: Duration, success: bool) {
        let status = if success { "success" } else { "failure" };
        let template_id_owned = template_id.to_string();

        histogram!("template_rendering_duration_seconds", duration.as_secs_f64(), "template_id" => template_id_owned.clone(), "status" => status);
        increment_counter!("template_rendering_total", "template_id" => template_id_owned, "status" => status);
    }
}

/// Initialize metrics registry and exporters
pub fn initialize_metrics() -> Result<(), Box<dyn std::error::Error>> {
    // Install the Prometheus recorder
    let builder = metrics_exporter_prometheus::PrometheusBuilder::new();
    let _handle = builder.install()?;

    // Register custom metrics
    register_custom_metrics();

    info!("Metrics system initialized with Prometheus exporter");
    Ok(())
}

/// Register custom application metrics
fn register_custom_metrics() {
    // HTTP metrics
    metrics::describe_counter!("http_requests_total", "Total number of HTTP requests");
    metrics::describe_counter!("http_responses_total", "Total number of HTTP responses");
    metrics::describe_gauge!("http_requests_active", "Number of active HTTP requests");
    metrics::describe_histogram!("http_request_duration_seconds", "HTTP request duration in seconds");

    // Business operation metrics
    metrics::describe_counter!("pdf_generation_total", "Total number of PDF generation attempts");
    metrics::describe_counter!("pdf_generation_success_total", "Total number of successful PDF generations");
    metrics::describe_counter!("pdf_generation_failure_total", "Total number of failed PDF generations");
    metrics::describe_histogram!("pdf_generation_duration_seconds", "PDF generation duration in seconds");
    metrics::describe_histogram!("pdf_file_size_bytes", "Generated PDF file size in bytes");

    // Storage metrics
    metrics::describe_counter!("storage_operations_total", "Total number of storage operations");
    metrics::describe_histogram!("storage_operation_duration_seconds", "Storage operation duration in seconds");
    metrics::describe_histogram!("storage_upload_size_bytes", "Storage upload size in bytes");

    // Database metrics
    metrics::describe_counter!("database_operations_total", "Total number of database operations");
    metrics::describe_histogram!("database_operation_duration_seconds", "Database operation duration in seconds");

    // Rate limiting metrics
    metrics::describe_counter!("rate_limit_checks_total", "Total number of rate limit checks");
    metrics::describe_counter!("rate_limit_blocks_total", "Total number of rate limit blocks");

    // Template metrics
    metrics::describe_counter!("template_rendering_total", "Total number of template rendering attempts");
    metrics::describe_histogram!("template_rendering_duration_seconds", "Template rendering duration in seconds");
}
