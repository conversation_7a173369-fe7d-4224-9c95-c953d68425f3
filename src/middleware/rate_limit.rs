use axum::{
    extract::{ConnectInfo, Request, State},
    http::StatusCode,
    middleware::Next,
    response::{IntoResponse, Response},
    Json,
};
use serde_json::json;
use std::net::SocketAddr;
use tracing::{debug, warn};

use crate::{error::AppError, state::AppState, utils};
use crate::middleware::monitoring::BusinessMetrics;

/// Rate limiting middleware
pub async fn rate_limit_middleware(
    ConnectInfo(addr): ConnectInfo<SocketAddr>,
    State(state): State<AppState>,
    req: Request,
    next: Next,
) -> Result<Response, AppError> {
    // Extract client IP from headers or connection info
    let ip = extract_client_ip(&req, addr);

    debug!("Rate limiting check for IP: {}", ip);

    // Check if rate limited
    match state.services.rate_limit.is_rate_limited(&ip).await {
        Ok(true) => {
            warn!("Rate limit exceeded for IP: {}", ip);

            // Record rate limit metrics
            BusinessMetrics::record_rate_limit_check(false, &ip);

            // Get reset time for retry-after header
            let retry_after = get_retry_after_seconds(&state, &ip).await;

            let response = Json(json!({
                "error": {
                    "code": "RATE_LIMIT_EXCEEDED",
                    "message": "Rate limit exceeded. Please try again later.",
                    "retry_after": retry_after
                }
            }));

            return Ok((
                StatusCode::TOO_MANY_REQUESTS,
                [("retry-after", retry_after.to_string())],
                response,
            ).into_response());
        }
        Ok(false) => {
            // Record rate limit metrics
            BusinessMetrics::record_rate_limit_check(true, &ip);

            // Increment request count for valid requests
            if let Err(e) = state.services.rate_limit.increment_request_count(&ip).await {
                warn!("Failed to increment rate limit counter for IP {}: {}", ip, e);
                // Continue anyway - don't fail the request for rate limit tracking errors
            }
        }
        Err(e) => {
            warn!("Rate limit check failed for IP {}: {}", ip, e);
            // Continue anyway - don't fail the request for rate limit check errors
        }
    }

    // Continue to next handler and add rate limit headers to response
    let mut response = next.run(req).await;

    // Add rate limit headers to successful responses
    if let Ok(remaining_info) = state.services.rate_limit.get_remaining_requests(&ip).await {
        let headers = response.headers_mut();

        // Add rate limit headers
        if let Ok(remaining_value) = axum::http::HeaderValue::from_str(&remaining_info.remaining.to_string()) {
            headers.insert("x-ratelimit-remaining", remaining_value);
        }

        if let Ok(limit_value) = axum::http::HeaderValue::from_str(&state.config.rate_limit.max_requests.to_string()) {
            headers.insert("x-ratelimit-limit", limit_value);
        }

        if let Some(reset_time) = remaining_info.reset_time {
            if let Ok(reset_value) = axum::http::HeaderValue::from_str(&reset_time.timestamp().to_string()) {
                headers.insert("x-ratelimit-reset", reset_value);
            }
        }
    }

    Ok(response)
}

/// Extract client IP from request headers or connection info
fn extract_client_ip(req: &Request, addr: SocketAddr) -> String {
    // Try to extract from headers first (for proxy scenarios)
    if let Some(ip) = utils::extract_client_ip(req.headers()) {
        return ip.to_string();
    }

    // Fall back to connection address
    addr.ip().to_string()
}

/// Get retry-after seconds for rate limited requests
async fn get_retry_after_seconds(state: &AppState, ip: &str) -> i64 {
    match state.services.rate_limit.get_remaining_requests(ip).await {
        Ok(remaining) => {
            if let Some(reset_time) = remaining.reset_time {
                (reset_time - chrono::Utc::now()).num_seconds().max(1)
            } else {
                60 // Default fallback
            }
        }
        Err(_) => 60, // Default fallback
    }
}