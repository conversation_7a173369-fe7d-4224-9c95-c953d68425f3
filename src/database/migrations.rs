use sea_orm_migration::prelude::*;

mod m20240101_000001_create_invoices_table;
mod m20240101_000002_create_invoice_items_table;
mod m20240101_000003_create_rate_limits_table;
mod m20240101_000004_add_indexes;

pub struct Migrator;

#[async_trait::async_trait]
impl MigratorTrait for Migrator {
    fn migrations() -> Vec<Box<dyn MigrationTrait>> {
        vec![
            Box::new(m20240101_000001_create_invoices_table::Migration),
            Box::new(m20240101_000002_create_invoice_items_table::Migration),
            Box::new(m20240101_000003_create_rate_limits_table::Migration),
            Box::new(m20240101_000004_add_indexes::Migration),
        ]
    }
} 