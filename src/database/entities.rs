// Database entities will be generated here by Sea ORM
// This is a placeholder file that will be populated when we implement the database models 

// Sea ORM entity models for the invoice generator API

pub mod invoice;
pub mod invoice_item;
pub mod rate_limit;

// Re-export entities for easier access
pub use invoice::Entity as Invoice;
pub use invoice_item::Entity as InvoiceItem;
pub use rate_limit::Entity as RateLimit; 