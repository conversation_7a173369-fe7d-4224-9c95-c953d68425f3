use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .create_table(
                Table::create()
                    .table(RateLimits::Table)
                    .if_not_exists()
                    .col(
                        ColumnDef::new(RateLimits::IpAddress)
                            .custom(Alias::new("inet"))
                            .not_null()
                            .primary_key(),
                    )
                    .col(
                        ColumnDef::new(RateLimits::RequestCount)
                            .integer()
                            .not_null()
                            .default(0),
                    )
                    .col(
                        ColumnDef::new(RateLimits::WindowStart)
                            .timestamp_with_time_zone()
                            .not_null()
                            .default(Expr::current_timestamp()),
                    )
                    .col(
                        ColumnDef::new(RateLimits::LastRequest)
                            .timestamp_with_time_zone()
                            .not_null()
                            .default(Expr::current_timestamp()),
                    )
                    .to_owned(),
            )
            .await
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .drop_table(Table::drop().table(RateLimits::Table).to_owned())
            .await
    }
}

#[derive(DeriveIden)]
enum RateLimits {
    Table,
    IpAddress,
    RequestCount,
    WindowStart,
    LastRequest,
} 