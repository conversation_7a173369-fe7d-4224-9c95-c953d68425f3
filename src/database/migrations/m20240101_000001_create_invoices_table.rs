use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .create_table(
                Table::create()
                    .table(Invoices::Table)
                    .if_not_exists()
                    .col(
                        ColumnDef::new(Invoices::Id)
                            .uuid()
                            .not_null()
                            .primary_key(),
                    )
                    .col(
                        ColumnDef::new(Invoices::TemplateId)
                            .string_len(50)
                            .null(),
                    )
                    .col(
                        ColumnDef::new(Invoices::InvoiceDate)
                            .date()
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Invoices::DueDate)
                            .date()
                            .null(),
                    )
                    .col(
                        ColumnDef::new(Invoices::Currency)
                            .string_len(3)
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Invoices::TotalAmount)
                            .decimal_len(10, 2)
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Invoices::TaxAmount)
                            .decimal_len(10, 2)
                            .null(),
                    )
                    .col(
                        ColumnDef::new(Invoices::GstAmount)
                            .decimal_len(10, 2)
                            .null(),
                    )
                    .col(
                        ColumnDef::new(Invoices::ShippingFee)
                            .decimal_len(10, 2)
                            .null(),
                    )
                    .col(
                        ColumnDef::new(Invoices::DiscountAmount)
                            .decimal_len(10, 2)
                            .null(),
                    )
                    .col(
                        ColumnDef::new(Invoices::BilledToName)
                            .string_len(255)
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Invoices::BilledToEmail)
                            .string_len(255)
                            .null(),
                    )
                    .col(
                        ColumnDef::new(Invoices::FromName)
                            .string_len(255)
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Invoices::FromEmail)
                            .string_len(255)
                            .null(),
                    )
                    .col(
                        ColumnDef::new(Invoices::FilePath)
                            .string_len(255)
                            .null(),
                    )
                    .col(
                        ColumnDef::new(Invoices::FileSize)
                            .big_integer()
                            .null(),
                    )
                    .col(
                        ColumnDef::new(Invoices::CreatedAt)
                            .timestamp_with_time_zone()
                            .not_null()
                            .default(Expr::current_timestamp()),
                    )
                    .col(
                        ColumnDef::new(Invoices::ExpiresAt)
                            .timestamp_with_time_zone()
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(Invoices::IpAddress)
                            .custom(Alias::new("inet"))
                            .not_null(),
                    )
                    .to_owned(),
            )
            .await
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .drop_table(Table::drop().table(Invoices::Table).to_owned())
            .await
    }
}

#[derive(DeriveIden)]
enum Invoices {
    Table,
    Id,
    TemplateId,
    InvoiceDate,
    DueDate,
    Currency,
    TotalAmount,
    TaxAmount,
    GstAmount,
    ShippingFee,
    DiscountAmount,
    BilledToName,
    BilledToEmail,
    FromName,
    FromEmail,
    FilePath,
    FileSize,
    CreatedAt,
    ExpiresAt,
    IpAddress,
} 