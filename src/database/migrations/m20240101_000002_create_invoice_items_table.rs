use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .create_table(
                Table::create()
                    .table(InvoiceItems::Table)
                    .if_not_exists()
                    .col(
                        ColumnDef::new(InvoiceItems::Id)
                            .uuid()
                            .not_null()
                            .primary_key(),
                    )
                    .col(
                        ColumnDef::new(InvoiceItems::InvoiceId)
                            .uuid()
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(InvoiceItems::Name)
                            .string_len(255)
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(InvoiceItems::Description)
                            .text()
                            .null(),
                    )
                    .col(
                        ColumnDef::new(InvoiceItems::Quantity)
                            .integer()
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(InvoiceItems::Price)
                            .decimal_len(10, 2)
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(InvoiceItems::Total)
                            .decimal_len(10, 2)
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(InvoiceItems::CreatedAt)
                            .timestamp_with_time_zone()
                            .not_null()
                            .default(Expr::current_timestamp()),
                    )
                    .foreign_key(
                        ForeignKey::create()
                            .name("fk_invoice_items_invoice_id")
                            .from(InvoiceItems::Table, InvoiceItems::InvoiceId)
                            .to(Invoices::Table, Invoices::Id)
                            .on_delete(ForeignKeyAction::Cascade)
                            .on_update(ForeignKeyAction::Cascade),
                    )
                    .to_owned(),
            )
            .await
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .drop_table(Table::drop().table(InvoiceItems::Table).to_owned())
            .await
    }
}

#[derive(DeriveIden)]
enum InvoiceItems {
    Table,
    Id,
    InvoiceId,
    Name,
    Description,
    Quantity,
    Price,
    Total,
    CreatedAt,
}

#[derive(DeriveIden)]
enum Invoices {
    Table,
    Id,
} 