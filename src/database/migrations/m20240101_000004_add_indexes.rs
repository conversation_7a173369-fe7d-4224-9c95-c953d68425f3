use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        // Index on invoices.expires_at for cleanup operations
        manager
            .create_index(
                Index::create()
                    .if_not_exists()
                    .name("idx_invoices_expires_at")
                    .table(Invoices::Table)
                    .col(Invoices::ExpiresAt)
                    .to_owned(),
            )
            .await?;

        // Index on invoices.created_at for sorting and filtering
        manager
            .create_index(
                Index::create()
                    .if_not_exists()
                    .name("idx_invoices_created_at")
                    .table(Invoices::Table)
                    .col(Invoices::CreatedAt)
                    .to_owned(),
            )
            .await?;

        // Index on invoices.template_id for template-based queries
        manager
            .create_index(
                Index::create()
                    .if_not_exists()
                    .name("idx_invoices_template_id")
                    .table(Invoices::Table)
                    .col(Invoices::TemplateId)
                    .to_owned(),
            )
            .await?;

        // Index on invoice_items.invoice_id for foreign key queries
        manager
            .create_index(
                Index::create()
                    .if_not_exists()
                    .name("idx_invoice_items_invoice_id")
                    .table(InvoiceItems::Table)
                    .col(InvoiceItems::InvoiceId)
                    .to_owned(),
            )
            .await?;

        // Index on rate_limits.window_start for cleanup operations
        manager
            .create_index(
                Index::create()
                    .if_not_exists()
                    .name("idx_rate_limits_window_start")
                    .table(RateLimits::Table)
                    .col(RateLimits::WindowStart)
                    .to_owned(),
            )
            .await?;

        Ok(())
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        // Drop indexes in reverse order
        manager
            .drop_index(Index::drop().name("idx_rate_limits_window_start").to_owned())
            .await?;

        manager
            .drop_index(Index::drop().name("idx_invoice_items_invoice_id").to_owned())
            .await?;

        manager
            .drop_index(Index::drop().name("idx_invoices_template_id").to_owned())
            .await?;

        manager
            .drop_index(Index::drop().name("idx_invoices_created_at").to_owned())
            .await?;

        manager
            .drop_index(Index::drop().name("idx_invoices_expires_at").to_owned())
            .await?;

        Ok(())
    }
}

#[derive(DeriveIden)]
enum Invoices {
    Table,
    ExpiresAt,
    CreatedAt,
    TemplateId,
}

#[derive(DeriveIden)]
enum InvoiceItems {
    Table,
    InvoiceId,
}

#[derive(DeriveIden)]
enum RateLimits {
    Table,
    WindowStart,
} 