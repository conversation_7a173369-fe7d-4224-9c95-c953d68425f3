use sea_orm::entity::prelude::*;
use sea_orm::{Set, ActiveValue};
use uuid::Uuid;
use serde::{Deserialize, Serialize};
use rust_decimal::Decimal;

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "invoice_items")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    
    pub invoice_id: Uuid,
    
    #[sea_orm(column_type = "String(Some(255))")]
    pub name: String,
    
    #[sea_orm(column_type = "Text", nullable)]
    pub description: Option<String>,
    
    pub quantity: i32,
    
    #[sea_orm(column_type = "Decimal(Some((10, 2)))")]
    pub price: Decimal,
    
    #[sea_orm(column_type = "Decimal(Some((10, 2)))")]
    pub total: Decimal,
    
    pub created_at: DateTimeWithTimeZone,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::invoice::Entity",
        from = "Column::InvoiceId",
        to = "super::invoice::Column::Id",
        on_update = "Cascade",
        on_delete = "Cascade"
    )]
    Invoice,
}

impl Related<super::invoice::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Invoice.def()
    }
}

#[async_trait::async_trait]
impl ActiveModelBehavior for ActiveModel {
    fn before_save<'life0, 'async_trait, C>(
        mut self,
        _db: &'life0 C,
        insert: bool,
    ) -> core::pin::Pin<Box<dyn core::future::Future<Output = Result<Self, DbErr>> + core::marker::Send + 'async_trait>>
    where
        'life0: 'async_trait,
        Self: 'async_trait,
        C: 'async_trait + ConnectionTrait,
    {
        Box::pin(async move {
            if insert {
                self.created_at = Set(chrono::Utc::now().into());
            }
            
            if let (ActiveValue::Set(quantity), ActiveValue::Set(price)) = (&self.quantity, &self.price) {
                let total = rust_decimal::Decimal::from(*quantity) * price;
                self.total = Set(total);
            }
            
            Ok(self)
        })
    }
} 