use sea_orm::entity::prelude::*;
use sea_orm::{Set, NotSet};
use serde::{Deserialize, Serialize};

#[derive(<PERSON>lone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "rate_limits")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false, column_type = "Inet")]
    pub ip_address: String,
    
    #[sea_orm(default_value = 0)]
    pub request_count: i32,
    
    pub window_start: DateTimeWithTimeZone,
    
    pub last_request: DateTimeWithTimeZone,
}

#[derive(Co<PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

#[async_trait::async_trait]
impl ActiveModelBehavior for ActiveModel {
    fn new() -> Self {
        let now = chrono::Utc::now().into();
        Self {
            ip_address: NotSet,
            request_count: Set(0),
            window_start: Set(now),
            last_request: Set(now),
        }
    }
    
    fn before_save<'life0, 'async_trait, C>(
        mut self,
        _db: &'life0 C,
        insert: bool,
    ) -> core::pin::Pin<Box<dyn core::future::Future<Output = Result<Self, DbErr>> + core::marker::Send + 'async_trait>>
    where
        'life0: 'async_trait,
        Self: 'async_trait,
        C: 'async_trait + ConnectionTrait,
    {
        Box::pin(async move {
            let now = chrono::Utc::now().into();
            
            if insert {
                self.window_start = Set(now);
                self.last_request = Set(now);
            } else {
                self.last_request = Set(chrono::Utc::now().into());
            }
            
            Ok(self)
        })
    }
} 