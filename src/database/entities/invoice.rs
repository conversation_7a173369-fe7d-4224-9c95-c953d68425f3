use sea_orm::entity::prelude::*;
use sea_orm::Set;
use uuid::Uuid;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "invoices")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    
    #[sea_orm(column_type = "String(Some(50))", nullable)]
    pub template_id: Option<String>,
    
    pub invoice_date: Date,
    
    pub due_date: Option<Date>,
    
    #[sea_orm(column_type = "String(Some(3))")]
    pub currency: String,
    
    #[sea_orm(column_type = "Decimal(Some((10, 2)))")]
    pub total_amount: Decimal,
    
    #[sea_orm(column_type = "Decimal(Some((10, 2)))", nullable)]
    pub tax_amount: Option<Decimal>,
    
    #[sea_orm(column_type = "Decimal(Some((10, 2)))", nullable)]
    pub gst_amount: Option<Decimal>,
    
    #[sea_orm(column_type = "Decimal(Some((10, 2)))", nullable)]
    pub shipping_fee: Option<Decimal>,
    
    #[sea_orm(column_type = "Decimal(Some((10, 2)))", nullable)]
    pub discount_amount: Option<Decimal>,
    
    #[sea_orm(column_type = "String(Some(255))")]
    pub billed_to_name: String,
    
    #[sea_orm(column_type = "String(Some(255))", nullable)]
    pub billed_to_email: Option<String>,
    
    #[sea_orm(column_type = "String(Some(255))")]
    pub from_name: String,
    
    #[sea_orm(column_type = "String(Some(255))", nullable)]
    pub from_email: Option<String>,
    
    #[sea_orm(column_type = "String(Some(255))", nullable)]
    pub file_path: Option<String>,
    
    pub file_size: Option<i64>,
    
    pub created_at: DateTimeWithTimeZone,
    
    pub expires_at: DateTimeWithTimeZone,
    
    #[sea_orm(column_type = "Inet")]
    pub ip_address: String,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::invoice_item::Entity")]
    InvoiceItems,
}

impl Related<super::invoice_item::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::InvoiceItems.def()
    }
}

#[async_trait::async_trait]
impl ActiveModelBehavior for ActiveModel {
    fn new() -> Self {
        Self {
            id: Set(Uuid::new_v4()),
            created_at: Set(chrono::Utc::now().into()),
            ..ActiveModelTrait::default()
        }
    }
    
    fn before_save<'life0, 'async_trait, C>(
        mut self,
        _db: &'life0 C,
        insert: bool,
    ) -> core::pin::Pin<Box<dyn core::future::Future<Output = Result<Self, DbErr>> + core::marker::Send + 'async_trait>>
    where
        'life0: 'async_trait,
        Self: 'async_trait,
        C: 'async_trait + ConnectionTrait,
    {
        Box::pin(async move {
            if insert {
                self.created_at = Set(chrono::Utc::now().into());
            }
            Ok(self)
        })
    }
} 