use sea_orm::{Database as SeaDatabase, DatabaseConnection, DbErr, ConnectOptions, ConnectionTrait};
use sea_orm_migration::MigratorTrait;
use tracing::{info, warn};
use std::time::Duration;

pub mod entities;
pub mod migrations;

use migrations::Migra<PERSON>;

#[derive(Clone)]
pub struct Database {
    pub connection: DatabaseConnection,
}

impl Database {
    pub async fn new(database_url: &str, max_connections: u32, min_connections: u32, timeout_secs: u64) -> Result<Self, DbErr> {
        info!("Connecting to database...");
        
        // Configure connection options
        let mut opt = ConnectOptions::new(database_url.to_owned());
        opt.max_connections(max_connections)
            .min_connections(min_connections)
            .connect_timeout(Duration::from_secs(timeout_secs))
            .acquire_timeout(Duration::from_secs(timeout_secs))
            .idle_timeout(Duration::from_secs(timeout_secs))
            .max_lifetime(Duration::from_secs(timeout_secs))
            .sqlx_logging(true)
            .sqlx_logging_level(log::LevelFilter::Info);

        let connection = SeaDatabase::connect(opt).await?;
        
        info!("Database connection established successfully with {} max connections", max_connections);
        
        Ok(Database { connection })
    }

    pub async fn migrate(&self) -> Result<(), DbErr> {
        info!("Running database migrations...");
        Migrator::up(&self.connection, None).await?;
        info!("Database migrations completed successfully");
        Ok(())
    }

    pub async fn rollback(&self, steps: Option<u32>) -> Result<(), DbErr> {
        warn!("Rolling back database migrations...");
        Migrator::down(&self.connection, steps).await?;
        info!("Database rollback completed");
        Ok(())
    }

    pub async fn refresh(&self) -> Result<(), DbErr> {
        info!("Refreshing database (dropping and recreating all tables)...");
        Migrator::fresh(&self.connection).await?;
        info!("Database refresh completed");
        Ok(())
    }

    pub fn get_connection(&self) -> &DatabaseConnection {
        &self.connection
    }

    pub async fn close(self) -> Result<(), DbErr> {
        info!("Closing database connection...");
        self.connection.close().await?;
        info!("Database connection closed");
        Ok(())
    }

    pub async fn test_connection(&self) -> Result<(), sea_orm::DbErr> {
        use sea_orm::{Statement, DatabaseBackend};
        
        let stmt = Statement::from_string(DatabaseBackend::Postgres, "SELECT 1".to_string());
        self.connection.execute(stmt).await?;
        Ok(())
    }
} 