use sea_orm::{
    ActiveModelTrait, ColumnTrait, DatabaseConnection, DbErr, EntityTrait, 
    QueryFilter, QueryOrder, Set, TransactionTrait, ActiveValue
};
use uuid::Uuid;
use chrono::Utc;

use crate::database::entities::{invoice_item, InvoiceItem};

pub struct InvoiceItemRepository;

impl InvoiceItemRepository {
    /// Create a new invoice item
    pub async fn create(
        db: &DatabaseConnection,
        item_data: CreateInvoiceItemData,
    ) -> Result<invoice_item::Model, DbErr> {
        let total = rust_decimal::Decimal::from(item_data.quantity) * item_data.price;
        
        let item = invoice_item::ActiveModel {
            id: Set(Uuid::new_v4()),
            invoice_id: Set(item_data.invoice_id),
            name: Set(item_data.name),
            description: Set(item_data.description),
            quantity: Set(item_data.quantity),
            price: Set(item_data.price),
            total: Set(total),
            created_at: Set(Utc::now().into()),
        };

        item.insert(db).await
    }

    /// Create multiple invoice items in a transaction
    pub async fn create_batch(
        db: &DatabaseConnection,
        items_data: Vec<CreateInvoiceItemData>,
    ) -> Result<Vec<invoice_item::Model>, DbErr> {
        let txn = db.begin().await?;
        let mut created_items = Vec::new();

        for item_data in items_data {
            let total = rust_decimal::Decimal::from(item_data.quantity) * item_data.price;
            
            let item = invoice_item::ActiveModel {
                id: Set(Uuid::new_v4()),
                invoice_id: Set(item_data.invoice_id),
                name: Set(item_data.name),
                description: Set(item_data.description),
                quantity: Set(item_data.quantity),
                price: Set(item_data.price),
                total: Set(total),
                created_at: Set(Utc::now().into()),
            };

            let created_item = item.insert(&txn).await?;
            created_items.push(created_item);
        }

        txn.commit().await?;
        Ok(created_items)
    }

    /// Find all items for an invoice
    pub async fn find_by_invoice_id(
        db: &DatabaseConnection,
        invoice_id: Uuid,
    ) -> Result<Vec<invoice_item::Model>, DbErr> {
        InvoiceItem::find()
            .filter(invoice_item::Column::InvoiceId.eq(invoice_id))
            .order_by_asc(invoice_item::Column::CreatedAt)
            .all(db)
            .await
    }

    /// Find an item by ID
    pub async fn find_by_id(
        db: &DatabaseConnection,
        id: Uuid,
    ) -> Result<Option<invoice_item::Model>, DbErr> {
        InvoiceItem::find_by_id(id).one(db).await
    }

    /// Update an invoice item
    pub async fn update(
        db: &DatabaseConnection,
        id: Uuid,
        update_data: UpdateInvoiceItemData,
    ) -> Result<invoice_item::Model, DbErr> {
        let mut item: invoice_item::ActiveModel = InvoiceItem::find_by_id(id)
            .one(db)
            .await?
            .ok_or(DbErr::Custom("Invoice item not found".to_string()))?
            .into();

        if let Some(name) = update_data.name {
            item.name = Set(name);
        }
        if let Some(description) = update_data.description {
            item.description = Set(description);
        }
        if let Some(quantity) = update_data.quantity {
            item.quantity = Set(quantity);
            // Recalculate total if quantity changed
            if let ActiveValue::Set(price) = &item.price {
                item.total = Set(rust_decimal::Decimal::from(quantity) * price);
            }
        }
        if let Some(price) = update_data.price {
            item.price = Set(price);
            // Recalculate total if price changed
            if let ActiveValue::Set(quantity) = &item.quantity {
                item.total = Set(rust_decimal::Decimal::from(*quantity) * price);
            }
        }

        item.update(db).await
    }

    /// Delete an invoice item
    pub async fn delete_by_id(db: &DatabaseConnection, id: Uuid) -> Result<(), DbErr> {
        InvoiceItem::delete_by_id(id).exec(db).await?;
        Ok(())
    }

    /// Delete all items for an invoice
    pub async fn delete_by_invoice_id(
        db: &DatabaseConnection,
        invoice_id: Uuid,
    ) -> Result<u64, DbErr> {
        let result = InvoiceItem::delete_many()
            .filter(invoice_item::Column::InvoiceId.eq(invoice_id))
            .exec(db)
            .await?;

        Ok(result.rows_affected)
    }

    /// Calculate total for all items in an invoice
    pub async fn calculate_total_for_invoice(
        db: &DatabaseConnection,
        invoice_id: Uuid,
    ) -> Result<rust_decimal::Decimal, DbErr> {
        let items = Self::find_by_invoice_id(db, invoice_id).await?;
        let total = items.iter().map(|item| item.total).sum();
        Ok(total)
    }
}

#[derive(Debug)]
pub struct CreateInvoiceItemData {
    pub invoice_id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub quantity: i32,
    pub price: rust_decimal::Decimal,
}

#[derive(Debug, Default)]
pub struct UpdateInvoiceItemData {
    pub name: Option<String>,
    pub description: Option<Option<String>>,
    pub quantity: Option<i32>,
    pub price: Option<rust_decimal::Decimal>,
} 