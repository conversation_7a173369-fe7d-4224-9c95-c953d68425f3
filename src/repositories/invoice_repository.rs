use sea_orm::{
    ActiveModelTrait, ColumnTrait, DatabaseConnection, DbErr, EntityTrait, 
    PaginatorTrait, QueryFilter, QueryOrder, Set, Condition, QuerySelect
};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use std::time::Instant;

use crate::database::entities::{invoice, Invoice};
use crate::middleware::monitoring::BusinessMetrics;

pub struct InvoiceRepository;

impl InvoiceRepository {
    /// Create a new invoice record
    pub async fn create(
        db: &DatabaseConnection,
        invoice_data: CreateInvoiceData,
    ) -> Result<invoice::Model, DbErr> {
        let start_time = Instant::now();

        let invoice = invoice::ActiveModel {
            id: Set(Uuid::new_v4()),
            template_id: Set(invoice_data.template_id),
            invoice_date: Set(invoice_data.invoice_date),
            due_date: Set(invoice_data.due_date),
            currency: Set(invoice_data.currency),
            total_amount: Set(invoice_data.total_amount),
            tax_amount: Set(invoice_data.tax_amount),
            gst_amount: Set(invoice_data.gst_amount),
            shipping_fee: Set(invoice_data.shipping_fee),
            discount_amount: Set(invoice_data.discount_amount),
            billed_to_name: Set(invoice_data.billed_to_name),
            billed_to_email: Set(invoice_data.billed_to_email),
            from_name: Set(invoice_data.from_name),
            from_email: Set(invoice_data.from_email),
            file_path: Set(invoice_data.file_path),
            file_size: Set(invoice_data.file_size),
            created_at: Set(Utc::now().into()),
            expires_at: Set(invoice_data.expires_at.into()),
            ip_address: Set(invoice_data.ip_address),
        };

        let result = invoice.insert(db).await;

        // Record database metrics
        let duration = start_time.elapsed();
        let success = result.is_ok();
        BusinessMetrics::record_database_operation("create", "invoice", duration, success);

        result
    }

    /// Find an invoice by ID
    pub async fn find_by_id(
        db: &DatabaseConnection,
        id: Uuid,
    ) -> Result<Option<invoice::Model>, DbErr> {
        Invoice::find_by_id(id).one(db).await
    }

    /// Find invoices by IP address
    pub async fn find_by_ip(
        db: &DatabaseConnection,
        ip_address: &str,
        limit: u64,
    ) -> Result<Vec<invoice::Model>, DbErr> {
        Invoice::find()
            .filter(invoice::Column::IpAddress.eq(ip_address))
            .order_by_desc(invoice::Column::CreatedAt)
            .limit(limit)
            .all(db)
            .await
    }

    /// Find expired invoices for cleanup
    pub async fn find_expired(
        db: &DatabaseConnection,
        limit: u64,
    ) -> Result<Vec<invoice::Model>, DbErr> {
        let now = Utc::now();
        
        Invoice::find()
            .filter(invoice::Column::ExpiresAt.lt(now))
            .limit(limit)
            .all(db)
            .await
    }

    /// Update file path after upload
    pub async fn update_file_path(
        db: &DatabaseConnection,
        id: Uuid,
        file_path: String,
        file_size: Option<i64>,
    ) -> Result<invoice::Model, DbErr> {
        let mut invoice: invoice::ActiveModel = Invoice::find_by_id(id)
            .one(db)
            .await?
            .ok_or(DbErr::Custom("Invoice not found".to_string()))?
            .into();

        invoice.file_path = Set(Some(file_path));
        if let Some(size) = file_size {
            invoice.file_size = Set(Some(size));
        }

        invoice.update(db).await
    }

    /// Delete an invoice by ID
    pub async fn delete_by_id(db: &DatabaseConnection, id: Uuid) -> Result<(), DbErr> {
        Invoice::delete_by_id(id).exec(db).await?;
        Ok(())
    }

    /// Delete expired invoices in batches
    pub async fn delete_expired_batch(
        db: &DatabaseConnection,
        _batch_size: u64, // Note: Sea ORM doesn't support LIMIT in DELETE, so we ignore batch_size for now
    ) -> Result<u64, DbErr> {
        let now = Utc::now();
        
        let result = Invoice::delete_many()
            .filter(invoice::Column::ExpiresAt.lt(now))
            .exec(db)
            .await?;

        Ok(result.rows_affected)
    }

    /// Count invoices by status or criteria
    pub async fn count_by_ip_in_timeframe(
        db: &DatabaseConnection,
        ip_address: &str,
        since: DateTime<Utc>,
    ) -> Result<u64, DbErr> {
        Invoice::find()
            .filter(
                Condition::all()
                    .add(invoice::Column::IpAddress.eq(ip_address))
                    .add(invoice::Column::CreatedAt.gte(since))
            )
            .count(db)
            .await
    }

    /// Get invoices with pagination
    pub async fn paginate(
        db: &DatabaseConnection,
        page: u64,
        per_page: u64,
    ) -> Result<(Vec<invoice::Model>, u64), DbErr> {
        let paginator = Invoice::find()
            .order_by_desc(invoice::Column::CreatedAt)
            .paginate(db, per_page);

        let total_pages = paginator.num_pages().await?;
        let invoices = paginator.fetch_page(page).await?;

        Ok((invoices, total_pages))
    }


}

#[derive(Debug)]
pub struct CreateInvoiceData {
    pub template_id: Option<String>,
    pub invoice_date: chrono::NaiveDate,
    pub due_date: Option<chrono::NaiveDate>,
    pub currency: String,
    pub total_amount: rust_decimal::Decimal,
    pub tax_amount: Option<rust_decimal::Decimal>,
    pub gst_amount: Option<rust_decimal::Decimal>,
    pub shipping_fee: Option<rust_decimal::Decimal>,
    pub discount_amount: Option<rust_decimal::Decimal>,
    pub billed_to_name: String,
    pub billed_to_email: Option<String>,
    pub from_name: String,
    pub from_email: Option<String>,
    pub file_path: Option<String>,
    pub file_size: Option<i64>,
    pub expires_at: DateTime<Utc>,
    pub ip_address: String,
} 