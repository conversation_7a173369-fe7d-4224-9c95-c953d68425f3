use sea_orm::{
    ActiveModelTrait, ColumnTrait, DatabaseConnection, DbErr, EntityTrait, 
    QueryFilter, Set, NotSet
};
use chrono::{DateTime, Utc, Duration};

use crate::database::entities::{rate_limit, RateLimit};

pub struct RateLimitRepository;

impl RateLimitRepository {
    /// Get or create rate limit record for an IP address
    pub async fn get_or_create(
        db: &DatabaseConnection,
        ip_address: &str,
    ) -> Result<rate_limit::Model, DbErr> {
        // Try to find existing record
        if let Some(existing) = Self::find_by_ip(db, ip_address).await? {
            return Ok(existing);
        }

        // Create new record if not found
        let rate_limit = rate_limit::ActiveModel {
            ip_address: Set(ip_address.to_string()),
            request_count: Set(0),
            window_start: Set(Utc::now().into()),
            last_request: Set(Utc::now().into()),
        };

        rate_limit.insert(db).await
    }

    /// Find rate limit record by IP address
    pub async fn find_by_ip(
        db: &DatabaseConnection,
        ip_address: &str,
    ) -> Result<Option<rate_limit::Model>, DbErr> {
        RateLimit::find_by_id(ip_address.to_string()).one(db).await
    }

    /// Increment request count for an IP address
    pub async fn increment_request_count(
        db: &DatabaseConnection,
        ip_address: &str,
        window_duration_minutes: i64,
    ) -> Result<RateLimitStatus, DbErr> {
        let mut rate_limit = Self::get_or_create(db, ip_address).await?;
        let now = Utc::now();
        let window_duration = Duration::minutes(window_duration_minutes);

        // Check if we need to reset the window
        let window_start: DateTime<Utc> = rate_limit.window_start.into();
        if now.signed_duration_since(window_start) > window_duration {
            // Reset the window
            rate_limit.request_count = 1;
            rate_limit.window_start = now.into();
        } else {
            // Increment count in current window
            rate_limit.request_count += 1;
        }

        rate_limit.last_request = now.into();

        // Update the record
        let active_model = rate_limit::ActiveModel {
            ip_address: Set(rate_limit.ip_address.clone()),
            request_count: Set(rate_limit.request_count),
            window_start: Set(rate_limit.window_start),
            last_request: Set(rate_limit.last_request),
        };
        let updated_rate_limit = active_model.update(db).await?;

        Ok(RateLimitStatus {
            ip_address: updated_rate_limit.ip_address,
            request_count: updated_rate_limit.request_count,
            window_start: updated_rate_limit.window_start.into(),
            last_request: updated_rate_limit.last_request.into(),
        })
    }

    /// Check if IP address is rate limited
    pub async fn is_rate_limited(
        db: &DatabaseConnection,
        ip_address: &str,
        max_requests: i32,
        window_duration_minutes: i64,
    ) -> Result<bool, DbErr> {
        let rate_limit = match Self::find_by_ip(db, ip_address).await? {
            Some(rl) => rl,
            None => return Ok(false), // No record means not rate limited
        };

        let now = Utc::now();
        let window_duration = Duration::minutes(window_duration_minutes);
        let window_start: DateTime<Utc> = rate_limit.window_start.into();

        // If window has expired, reset and allow
        if now.signed_duration_since(window_start) > window_duration {
            return Ok(false);
        }

        // Check if current count exceeds limit
        Ok(rate_limit.request_count >= max_requests)
    }

    /// Get remaining requests for an IP address
    pub async fn get_remaining_requests(
        db: &DatabaseConnection,
        ip_address: &str,
        max_requests: i32,
        window_duration_minutes: i64,
    ) -> Result<RemainingRequests, DbErr> {
        let rate_limit = match Self::find_by_ip(db, ip_address).await? {
            Some(rl) => rl,
            None => {
                return Ok(RemainingRequests {
                    remaining: max_requests,
                    reset_time: None,
                });
            }
        };

        let now = Utc::now();
        let window_duration = Duration::minutes(window_duration_minutes);
        let window_start: DateTime<Utc> = rate_limit.window_start.into();

        // If window has expired, full limit available
        if now.signed_duration_since(window_start) > window_duration {
            return Ok(RemainingRequests {
                remaining: max_requests,
                reset_time: None,
            });
        }

        let remaining = (max_requests - rate_limit.request_count).max(0);
        let reset_time = window_start + window_duration;

        Ok(RemainingRequests {
            remaining,
            reset_time: Some(reset_time),
        })
    }

    /// Clean up expired rate limit records
    pub async fn cleanup_expired(
        db: &DatabaseConnection,
        window_duration_minutes: i64,
    ) -> Result<u64, DbErr> {
        let cutoff_time = Utc::now() - Duration::minutes(window_duration_minutes);

        let result = RateLimit::delete_many()
            .filter(rate_limit::Column::WindowStart.lt(cutoff_time))
            .exec(db)
            .await?;

        Ok(result.rows_affected)
    }

    /// Delete rate limit record for an IP address
    pub async fn delete_by_ip(db: &DatabaseConnection, ip_address: &str) -> Result<(), DbErr> {
        RateLimit::delete_by_id(ip_address.to_string()).exec(db).await?;
        Ok(())
    }

    /// Reset rate limit for an IP address (admin function)
    pub async fn reset_for_ip(db: &DatabaseConnection, ip_address: &str) -> Result<(), DbErr> {
        let mut rate_limit = match Self::find_by_ip(db, ip_address).await? {
            Some(rl) => rl,
            None => return Ok(()), // No record to reset
        };

        rate_limit.request_count = 0;
        rate_limit.window_start = Utc::now().into();

        let mut active_model: rate_limit::ActiveModel = rate_limit.into();
        active_model.ip_address = NotSet; // Don't update the primary key
        active_model.update(db).await?;

        Ok(())
    }
}

#[derive(Debug)]
pub struct RateLimitStatus {
    pub ip_address: String,
    pub request_count: i32,
    pub window_start: DateTime<Utc>,
    pub last_request: DateTime<Utc>,
}

#[derive(Debug)]
pub struct RemainingRequests {
    pub remaining: i32,
    pub reset_time: Option<DateTime<Utc>>,
} 