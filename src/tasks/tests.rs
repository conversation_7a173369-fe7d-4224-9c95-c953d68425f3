#[cfg(test)]
mod tests {
    use std::sync::Arc;
    use crate::{
        config::Config,
    };

    // Helper function to create a test config
    fn create_test_config() -> Config {
        Config {
            port: 3000,
            database_url: "postgresql://test:test@localhost:5432/test_db".to_string(),
            r2_endpoint: "https://test.r2.cloudflarestorage.com".to_string(),
            r2_access_key: "test_key".to_string(),
            r2_secret_key: "test_secret".to_string(),
            r2_bucket_name: "test-bucket".to_string(),
            r2_region: "auto".to_string(),
            rate_limit: crate::config::RateLimitConfig {
                max_requests: 5,
                window_duration_minutes: 60,
            },
            pdf_retention_days: 7,
            max_request_size: 1048576,
            wkhtmltopdf_path: None,
            database_max_connections: 10,
            database_min_connections: 1,
            database_connect_timeout: 8,
            security: crate::config::SecurityConfig {
                enable_hsts: true,
                enable_csp: true,
                request_timeout_seconds: 30,
                allowed_origins: vec!["*".to_string()],
                enable_security_headers: true,
            },
            monitoring: crate::config::MonitoringConfig {
                enable_metrics: true,
                enable_tracing: true,
                log_level: "info".to_string(),
                enable_correlation_ids: true,
                enable_request_logging: true,
                metrics_endpoint_enabled: true,
            },
        }
    }

    #[tokio::test]
    async fn test_cleanup_task_creation() {
        let config = Arc::new(create_test_config());
        
        // Note: This test doesn't actually connect to a database or R2
        // It just tests that the CleanupTask can be created without panicking
        
        // We can't easily test the actual cleanup functionality without a real database
        // and R2 connection, but we can test the structure
        
        // This would require a real database connection:
        // let database = Database::new(&config.database_url, 10, 1, 8).await.unwrap();
        // let storage = StorageService::new(&config).await.unwrap();
        // let cleanup_task = CleanupTask::new(config, database.get_connection().clone(), storage);
        
        // For now, just test that the config is valid
        assert_eq!(config.pdf_retention_days, 7);
        assert_eq!(config.rate_limit.max_requests, 5);
        assert_eq!(config.rate_limit.window_duration_minutes, 60);
    }

    #[test]
    fn test_config_values() {
        let config = create_test_config();
        
        // Test that cleanup-related config values are reasonable
        assert!(config.pdf_retention_days > 0);
        assert!(config.rate_limit.window_duration_minutes > 0);
        assert!(config.rate_limit.max_requests > 0);
    }

    // Integration tests would go here if we had a test database setup
    // For example:
    // - Test that expired invoices are actually deleted
    // - Test that expired rate limit entries are cleaned up
    // - Test that expired PDF files are removed from R2
    // - Test that the scheduler runs tasks at the correct intervals
}
