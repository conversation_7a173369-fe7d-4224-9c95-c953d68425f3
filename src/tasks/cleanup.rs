use std::sync::Arc;
use tracing::{info, warn, error};
use sea_orm::DatabaseConnection;

use crate::{
    config::Config,
    repositories::{
        invoice_repository::InvoiceRepository,
        rate_limit_repository::RateLimitRepository,
    },
    services::storage::StorageService,
    error::AppResult,
};

/// Cleanup task for managing expired data
#[derive(Clone)]
pub struct CleanupTask {
    config: Arc<Config>,
    db: DatabaseConnection,
    storage: StorageService,
}

impl CleanupTask {
    /// Create a new cleanup task
    pub fn new(config: Arc<Config>, db: DatabaseConnection, storage: StorageService) -> Self {
        Self {
            config,
            db,
            storage,
        }
    }

    /// Run daily cleanup tasks
    /// - Delete expired invoices from database
    /// - Delete expired PDF files from R2 storage
    pub async fn run_daily_cleanup(&self) -> AppResult<()> {
        info!("Starting daily cleanup tasks");

        let mut total_errors = 0;

        // Clean up expired invoices
        match self.cleanup_expired_invoices().await {
            Ok(deleted_count) => {
                info!("Daily cleanup: Deleted {} expired invoices", deleted_count);
            }
            Err(e) => {
                error!("Failed to cleanup expired invoices: {}", e);
                total_errors += 1;
            }
        }

        // Clean up expired PDF files
        match self.cleanup_expired_pdfs().await {
            Ok(deleted_files) => {
                info!("Daily cleanup: Deleted {} expired PDF files", deleted_files.len());
                if !deleted_files.is_empty() {
                    info!("Deleted files: {:?}", deleted_files);
                }
            }
            Err(e) => {
                error!("Failed to cleanup expired PDF files: {}", e);
                total_errors += 1;
            }
        }

        if total_errors > 0 {
            warn!("Daily cleanup completed with {} errors", total_errors);
        } else {
            info!("Daily cleanup completed successfully");
        }

        Ok(())
    }

    /// Run hourly cleanup tasks
    /// - Clean up expired rate limit entries
    pub async fn run_hourly_cleanup(&self) -> AppResult<()> {
        info!("Starting hourly cleanup tasks");

        match self.cleanup_expired_rate_limits().await {
            Ok(deleted_count) => {
                info!("Hourly cleanup: Deleted {} expired rate limit entries", deleted_count);
            }
            Err(e) => {
                error!("Failed to cleanup expired rate limits: {}", e);
                return Err(e);
            }
        }

        info!("Hourly cleanup completed successfully");
        Ok(())
    }

    /// Clean up expired invoices from the database
    async fn cleanup_expired_invoices(&self) -> AppResult<u64> {
        info!("Cleaning up expired invoices");

        // Use a reasonable batch size to avoid overwhelming the database
        const BATCH_SIZE: u64 = 1000;

        let deleted_count = InvoiceRepository::delete_expired_batch(&self.db, BATCH_SIZE)
            .await
            .map_err(|e| {
                error!("Database error during invoice cleanup: {}", e);
                crate::error::AppError::Database(e)
            })?;

        Ok(deleted_count)
    }

    /// Clean up expired PDF files from R2 storage
    async fn cleanup_expired_pdfs(&self) -> AppResult<Vec<String>> {
        info!("Cleaning up expired PDF files from R2 storage");

        let deleted_files = self.storage
            .delete_expired_files(self.config.pdf_retention_days)
            .await
            .map_err(|e| {
                error!("Storage error during PDF cleanup: {}", e);
                crate::error::AppError::Storage(e)
            })?;

        Ok(deleted_files)
    }

    /// Clean up expired rate limit entries
    async fn cleanup_expired_rate_limits(&self) -> AppResult<u64> {
        info!("Cleaning up expired rate limit entries");

        let deleted_count = RateLimitRepository::cleanup_expired(
            &self.db,
            self.config.rate_limit.window_duration_minutes,
        )
        .await
        .map_err(|e| {
            error!("Database error during rate limit cleanup: {}", e);
            crate::error::AppError::Database(e)
        })?;

        Ok(deleted_count)
    }

    /// Run a comprehensive cleanup (useful for manual triggers or startup)
    pub async fn run_full_cleanup(&self) -> AppResult<()> {
        info!("Starting full cleanup (daily + hourly tasks)");

        // Run both daily and hourly cleanup tasks
        if let Err(e) = self.run_daily_cleanup().await {
            error!("Daily cleanup failed during full cleanup: {}", e);
        }

        if let Err(e) = self.run_hourly_cleanup().await {
            error!("Hourly cleanup failed during full cleanup: {}", e);
        }

        info!("Full cleanup completed");
        Ok(())
    }
}
