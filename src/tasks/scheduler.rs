use std::sync::Arc;
use std::time::Duration;
use tokio::time::{interval, MissedTickBehavior};
use tracing::{info, error};

use crate::{
    config::Config,
    database::Database,
    services::storage::StorageService,
    tasks::cleanup::CleanupTask,
    error::AppResult,
};

/// Task scheduler for running background cleanup tasks
pub struct TaskScheduler {
    cleanup_task: CleanupTask,
}

impl TaskScheduler {
    /// Create a new task scheduler
    pub fn new(config: Arc<Config>, database: &Database, storage: StorageService) -> Self {
        let cleanup_task = CleanupTask::new(
            config,
            database.get_connection().clone(),
            storage,
        );

        Self { cleanup_task }
    }

    /// Start the task scheduler
    /// This will spawn background tasks for periodic cleanup operations
    pub fn start(&self) -> AppResult<()> {
        info!("Starting task scheduler");

        // Clone the cleanup task for use in spawned tasks
        let daily_cleanup = self.cleanup_task.clone();
        let hourly_cleanup = self.cleanup_task.clone();

        // Spawn daily cleanup task
        tokio::spawn(async move {
            Self::run_daily_scheduler(daily_cleanup).await;
        });

        // Spawn hourly cleanup task
        tokio::spawn(async move {
            Self::run_hourly_scheduler(hourly_cleanup).await;
        });

        info!("Task scheduler started successfully");
        Ok(())
    }

    /// Run the daily cleanup scheduler
    async fn run_daily_scheduler(cleanup_task: CleanupTask) {
        // Run daily cleanup every 24 hours
        let mut interval = interval(Duration::from_secs(24 * 60 * 60)); // 24 hours
        interval.set_missed_tick_behavior(MissedTickBehavior::Skip);

        info!("Daily cleanup scheduler started (runs every 24 hours)");

        // Run initial cleanup after a short delay to allow the application to fully start
        tokio::time::sleep(Duration::from_secs(30)).await;
        
        loop {
            interval.tick().await;
            
            info!("Triggering daily cleanup task");
            if let Err(e) = cleanup_task.run_daily_cleanup().await {
                error!("Daily cleanup task failed: {}", e);
            }
        }
    }

    /// Run the hourly cleanup scheduler
    async fn run_hourly_scheduler(cleanup_task: CleanupTask) {
        // Run hourly cleanup every hour
        let mut interval = interval(Duration::from_secs(60 * 60)); // 1 hour
        interval.set_missed_tick_behavior(MissedTickBehavior::Skip);

        info!("Hourly cleanup scheduler started (runs every hour)");

        // Run initial cleanup after a short delay to allow the application to fully start
        tokio::time::sleep(Duration::from_secs(60)).await;

        loop {
            interval.tick().await;
            
            info!("Triggering hourly cleanup task");
            if let Err(e) = cleanup_task.run_hourly_cleanup().await {
                error!("Hourly cleanup task failed: {}", e);
            }
        }
    }

    /// Run an immediate full cleanup (useful for testing or manual triggers)
    pub async fn run_immediate_cleanup(&self) -> AppResult<()> {
        info!("Running immediate cleanup");
        self.cleanup_task.run_full_cleanup().await
    }
}


