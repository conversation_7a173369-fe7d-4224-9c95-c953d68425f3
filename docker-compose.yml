version: "3.8"

services:
  # PostgreSQL database
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: invoice_generator
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  # Invoice Generator API
  api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      DATABASE_URL: ********************************************/invoice_generator
      PORT: 3000
      R2_ENDPOINT: https://your-account-id.r2.cloudflarestorage.com
      R2_ACCESS_KEY: your-access-key
      R2_SECRET_KEY: your-secret-key
      R2_BUCKET_NAME: invoice-pdfs
      R2_REGION: auto
      RATE_LIMIT_REQUESTS: 5
      RATE_LIMIT_WINDOW_SECONDS: 60
      PDF_RETENTION_DAYS: 7
      MAX_REQUEST_SIZE: 1048576
      RUST_LOG: debug
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./templates:/app/templates
    restart: unless-stopped

  # Adminer for database management (optional)
  adminer:
    image: adminer
    ports:
      - "8080:8080"
    environment:
      ADMINER_DEFAULT_SERVER: postgres
    depends_on:
      - postgres

volumes:
  postgres_data:
