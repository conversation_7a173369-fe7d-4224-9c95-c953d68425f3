[package]
name = "invoice-generator-api"
version = "0.1.0"
edition = "2021"

[dependencies]
# Web framework
axum = "0.7"
tower = { version = "0.4", features = ["util"] }
tower-http = { version = "0.5", features = ["cors", "fs", "trace"] }
tokio = { version = "1.0", features = ["full"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Database
sea-orm = { version = "0.12", features = ["sqlx-postgres", "sqlx-sqlite", "runtime-tokio-rustls", "macros", "with-chrono", "with-uuid"] }
sea-orm-migration = "0.12"
sqlx = { version = "0.7", features = ["postgres", "sqlite", "runtime-tokio-rustls", "chrono", "uuid"] }

# Date/Time
chrono = { version = "0.4", features = ["serde"] }

# UUID
uuid = { version = "1.0", features = ["v4", "serde"] }

# Environment variables
dotenv = "0.15"

# Logging and Monitoring
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
log = "0.4"
metrics = "0.21"
metrics-exporter-prometheus = "0.12"

# Decimal arithmetic
rust_decimal = "1.32"
rust_decimal_macros = "1.32"

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# HTTP client for R2
reqwest = { version = "0.11", features = ["json", "rustls-tls"] }

# PDF generation with headless Chrome
headless_chrome = "1.0"
tempfile = "3.8"
base64 = "0.21"

# Template engine
handlebars = "4.5"

# Validation
validator = { version = "0.16", features = ["derive"] }
regex = "1.10"

# Rate limiting
dashmap = "5.5"

# Configuration
config = "0.13"

# AWS SDK for R2 (S3-compatible)
aws-config = "1.0"
aws-sdk-s3 = "1.0"

# Security
argon2 = "0.5"

# Async utilities
futures = "0.3"
async-trait = "0.1"

# IP address handling
ipnet = "2.9"

[dev-dependencies]
tokio-test = "0.4"
