# Logging and Monitoring

This document describes the comprehensive logging and monitoring system implemented for the Invoice Generator API.

## Overview

The application implements a multi-layered monitoring approach including:

- **Structured Logging**: Using `tracing` for hierarchical, structured logs
- **Correlation IDs**: Request tracing across service boundaries
- **Metrics Collection**: Prometheus-compatible metrics for performance monitoring
- **Request/Response Logging**: Comprehensive HTTP request lifecycle tracking
- **Business Metrics**: Domain-specific metrics for PDF generation, storage operations, etc.

## Configuration

Monitoring features can be configured via environment variables:

```env
# Monitoring Configuration
ENABLE_METRICS=true                    # Enable Prometheus metrics collection
ENABLE_TRACING=true                    # Enable structured logging
LOG_LEVEL=info                         # Log level (debug, info, warn, error)
ENABLE_CORRELATION_IDS=true            # Enable request correlation IDs
ENABLE_REQUEST_LOGGING=true            # Enable request/response logging middleware
METRICS_ENDPOINT_ENABLED=true          # Expose /metrics endpoint
```

## Logging Levels

The application uses structured logging with the following levels:

- **ERROR**: System errors, failed operations, critical issues
- **WARN**: Rate limiting, client errors, degraded performance
- **INFO**: Normal operations, successful requests, business events
- **DEBUG**: Detailed execution flow, internal state changes

### Log Format

All logs include:
- Timestamp
- Log level
- Target module
- Thread ID
- File and line number
- Correlation ID (when available)
- Structured fields

Example log entry:
```
2024-01-15T10:30:45.123Z INFO invoice_generator_api::handlers::invoices [thread:tokio-runtime-worker-1] src/handlers/invoices.rs:32 correlation_id="550e8400-e29b-41d4-a716-446655440000" method="POST" path="/api/v1/invoices" client_ip="*************" "Request started"
```

## Correlation IDs

Every request is assigned a unique correlation ID that:

- Propagates through all service layers
- Appears in all log entries for that request
- Is returned in response headers as `x-correlation-id`
- Can be provided by clients in request headers

This enables end-to-end request tracing across the entire system.

## Metrics Collection

### HTTP Metrics

- `http_requests_total`: Total HTTP requests by method, path, and status
- `http_responses_total`: Total HTTP responses by method, path, and status  
- `http_requests_active`: Current number of active requests
- `http_request_duration_seconds`: Request duration histogram

### Business Metrics

#### PDF Generation
- `pdf_generation_total`: Total PDF generation attempts by status
- `pdf_generation_success_total`: Successful PDF generations
- `pdf_generation_failure_total`: Failed PDF generations
- `pdf_generation_duration_seconds`: PDF generation time
- `pdf_file_size_bytes`: Generated PDF file sizes

#### Storage Operations
- `storage_operations_total`: Total storage operations by type and status
- `storage_operation_duration_seconds`: Storage operation duration
- `storage_upload_size_bytes`: Upload file sizes

#### Database Operations
- `database_operations_total`: Database operations by type, table, and status
- `database_operation_duration_seconds`: Database operation duration

#### Rate Limiting
- `rate_limit_checks_total`: Rate limit checks by status (allowed/blocked)
- `rate_limit_blocks_total`: Total rate limit violations

#### Template Rendering
- `template_rendering_total`: Template rendering attempts by template and status
- `template_rendering_duration_seconds`: Template rendering duration

### Metrics Endpoint

Metrics are exposed at `/metrics` in Prometheus format when enabled:

```
curl http://localhost:3000/metrics
```

## Middleware Stack

The monitoring middleware is applied in the following order:

1. **TraceLayer**: HTTP request tracing (from tower-http)
2. **Request Logging Middleware**: Detailed request/response logging with metrics
3. **Correlation ID Middleware**: Assigns and propagates correlation IDs
4. **CORS Layer**: Cross-origin resource sharing
5. **Security Headers**: Security-related HTTP headers
6. **Request Size Middleware**: Request size validation
7. **Request Timeout Middleware**: Request timeout handling
8. **Rate Limiting Middleware**: Rate limiting with metrics

## Implementation Details

### Structured Logging

The application uses the `tracing` crate for structured logging:

```rust
use tracing::{info, warn, error, debug};

info!(
    correlation_id = %correlation_id,
    user_id = %user_id,
    operation = "pdf_generation",
    duration_ms = duration.as_millis(),
    "PDF generation completed successfully"
);
```

### Metrics Recording

Business metrics are recorded using the `metrics` crate:

```rust
use crate::middleware::monitoring::BusinessMetrics;

// Record PDF generation metrics
BusinessMetrics::record_pdf_generation(duration, success, file_size);

// Record storage operation metrics  
BusinessMetrics::record_storage_operation("upload", duration, success, file_size);
```

### Correlation ID Propagation

Correlation IDs are automatically:
- Generated for new requests
- Extracted from incoming `x-correlation-id` headers
- Added to all log entries within the request scope
- Included in response headers
- Passed to downstream services

## Monitoring Best Practices

### Log Sanitization

All user input is sanitized before logging to prevent log injection:

```rust
use crate::middleware::security::sanitization;

info!("Request from IP: {}", sanitization::sanitize_for_logging(&client_ip));
```

### Error Handling

Errors are categorized and logged appropriately:
- Internal errors (database, storage, PDF generation) are logged as ERROR
- Client errors (validation, bad requests) are logged as WARN
- Rate limiting is logged as WARN with client IP

### Performance Impact

The monitoring system is designed for minimal performance impact:
- Metrics collection uses lock-free data structures
- Logging is asynchronous where possible
- Expensive operations (like metrics export) are done on-demand

## Alerting and Dashboards

### Recommended Alerts

- High error rate (>5% 5xx responses)
- High response time (>2s p95)
- PDF generation failures (>10% failure rate)
- Storage operation failures
- Rate limiting threshold breaches

### Dashboard Metrics

Key metrics to monitor:
- Request rate and response times
- Error rates by endpoint
- PDF generation success rate and timing
- Storage operation performance
- Database operation performance
- Active connections and resource usage

## Troubleshooting

### Common Issues

1. **Missing Correlation IDs**: Check that correlation_id_middleware is enabled
2. **No Metrics**: Verify ENABLE_METRICS=true and /metrics endpoint is accessible
3. **Verbose Logs**: Adjust LOG_LEVEL environment variable
4. **Performance Impact**: Consider disabling detailed request logging in production

### Debug Mode

Enable debug logging for detailed troubleshooting:

```env
LOG_LEVEL=debug
```

This will include detailed execution flow, timing information, and internal state changes.
