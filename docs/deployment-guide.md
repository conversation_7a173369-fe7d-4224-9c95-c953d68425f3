# Deployment Guide

This guide covers various deployment options for the Invoice Generator API, from local development to production environments.

## Table of Contents

- [Quick Start with Docker Compose](#quick-start-with-docker-compose)
- [Local Development Setup](#local-development-setup)
- [Production Deployment](#production-deployment)
- [Environment Configuration](#environment-configuration)
- [Database Setup](#database-setup)
- [Cloudflare R2 Setup](#cloudflare-r2-setup)
- [Monitoring and Logging](#monitoring-and-logging)
- [Troubleshooting](#troubleshooting)

## Quick Start with Docker Compose

The fastest way to get the Invoice Generator API running is with Docker Compose.

### Prerequisites

- Docker and Docker Compose installed
- Cloudflare R2 account and credentials

### Steps

1. **Clone the repository**:

   ```bash
   git clone <repository-url>
   cd invoice-generator-api
   ```

2. **Configure environment**:

   ```bash
   cp .env.example .env
   # Edit .env with your Cloudflare R2 credentials
   ```

3. **Start services**:

   ```bash
   docker-compose up -d
   ```

4. **Verify deployment**:
   ```bash
   curl http://localhost:3000/health
   ```

The API will be available at `http://localhost:3000` with:

- PostgreSQL database on port 5432
- Adminer (database UI) on port 8080

## Local Development Setup

For development with hot reloading and debugging capabilities.

### Prerequisites

- Rust (latest stable version)
- PostgreSQL 12+
- Git

### Installation Steps

1. **Install Rust**:

   ```bash
   curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
   source ~/.cargo/env
   ```

2. **Install PostgreSQL**:

   ```bash
   # macOS
   brew install postgresql
   brew services start postgresql

   # Ubuntu/Debian
   sudo apt update
   sudo apt install postgresql postgresql-contrib
   sudo systemctl start postgresql

   # Windows
   # Download from https://www.postgresql.org/download/windows/
   ```

3. **Clone and setup project**:

   ```bash
   git clone <repository-url>
   cd invoice-generator-api
   cp .env.example .env
   ```

4. **Create database**:

   ```bash
   createdb invoice_generator
   # Or using psql:
   psql -c "CREATE DATABASE invoice_generator;"
   ```

5. **Configure environment**:
   Edit `.env` file with your settings:

   ```env
   DATABASE_URL=postgresql://username:password@localhost:5432/invoice_generator
   R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
   R2_ACCESS_KEY=your-access-key
   R2_SECRET_KEY=your-secret-key
   R2_BUCKET_NAME=invoice-pdfs
   ```

6. **Run the application**:
   ```bash
   cargo run
   ```

### Development Commands

```bash
# Run with debug logging
RUST_LOG=debug cargo run

# Run tests
cargo test

# Format code
cargo fmt

# Lint code
cargo clippy

# Build for release
cargo build --release
```

## Production Deployment

### Option 1: Docker Container

1. **Build production image**:

   ```bash
   docker build -t invoice-generator-api:latest .
   ```

2. **Run container**:
   ```bash
   docker run -d \
     --name invoice-api \
     -p 3000:3000 \
     --env-file .env \
     invoice-generator-api:latest
   ```

### Option 2: Binary Deployment

1. **Build release binary**:

   ```bash
   cargo build --release
   ```

2. **Copy binary to server**:

   ```bash
   scp target/release/invoice-generator-api user@server:/opt/invoice-api/
   ```

3. **Create systemd service** (`/etc/systemd/system/invoice-api.service`):

   ```ini
   [Unit]
   Description=Invoice Generator API
   After=network.target postgresql.service

   [Service]
   Type=simple
   User=invoice-api
   WorkingDirectory=/opt/invoice-api
   ExecStart=/opt/invoice-api/invoice-generator-api
   EnvironmentFile=/opt/invoice-api/.env
   Restart=always
   RestartSec=5

   [Install]
   WantedBy=multi-user.target
   ```

4. **Start service**:
   ```bash
   sudo systemctl enable invoice-api
   sudo systemctl start invoice-api
   ```

### Option 3: Cloud Deployment

#### AWS ECS/Fargate

1. **Create task definition**:
   ```json
   {
     "family": "invoice-generator-api",
     "networkMode": "awsvpc",
     "requiresCompatibilities": ["FARGATE"],
     "cpu": "256",
     "memory": "512",
     "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
     "containerDefinitions": [
       {
         "name": "invoice-api",
         "image": "your-registry/invoice-generator-api:latest",
         "portMappings": [
           {
             "containerPort": 3000,
             "protocol": "tcp"
           }
         ],
         "environment": [
           {
             "name": "DATABASE_URL",
             "value": "****************************************/invoice_generator"
           }
         ],
         "logConfiguration": {
           "logDriver": "awslogs",
           "options": {
             "awslogs-group": "/ecs/invoice-generator-api",
             "awslogs-region": "us-east-1",
             "awslogs-stream-prefix": "ecs"
           }
         }
       }
     ]
   }
   ```

#### Google Cloud Run

1. **Deploy to Cloud Run**:
   ```bash
   gcloud run deploy invoice-generator-api \
     --image gcr.io/project-id/invoice-generator-api \
     --platform managed \
     --region us-central1 \
     --allow-unauthenticated \
     --port 3000 \
     --set-env-vars DATABASE_URL="postgresql://..." \
     --set-env-vars R2_ENDPOINT="https://..." \
     --memory 512Mi \
     --cpu 1
   ```

#### DigitalOcean App Platform

1. **Create app spec** (`.do/app.yaml`):
   ```yaml
   name: invoice-generator-api
   services:
     - name: api
       source_dir: /
       github:
         repo: your-username/invoice-generator-api
         branch: main
       run_command: ./target/release/invoice-generator-api
       environment_slug: rust
       instance_count: 1
       instance_size_slug: basic-xxs
       envs:
         - key: DATABASE_URL
           value: ${db.DATABASE_URL}
         - key: R2_ENDPOINT
           value: https://your-account-id.r2.cloudflarestorage.com
       http_port: 3000
   databases:
     - name: db
       engine: PG
       version: "13"
   ```

## Environment Configuration

### Required Variables

```env
# Database (Required)
DATABASE_URL=postgresql://user:pass@host:port/database

# Cloudflare R2 (Required)
R2_ENDPOINT=https://account-id.r2.cloudflarestorage.com
R2_ACCESS_KEY=your-access-key
R2_SECRET_KEY=your-secret-key
R2_BUCKET_NAME=invoice-pdfs
```

### Optional Variables

```env
# Server
PORT=3000

# Database Pool
DATABASE_MAX_CONNECTIONS=100
DATABASE_MIN_CONNECTIONS=5
DATABASE_CONNECT_TIMEOUT=8

# Rate Limiting
RATE_LIMIT_REQUESTS=5
RATE_LIMIT_WINDOW_SECONDS=60

# PDF Settings
PDF_RETENTION_DAYS=7
MAX_REQUEST_SIZE=1048576

# Security
ENABLE_HSTS=true
ENABLE_CSP=true
REQUEST_TIMEOUT_SECONDS=30
ALLOWED_ORIGINS=*
ENABLE_SECURITY_HEADERS=true

# Monitoring
ENABLE_METRICS=true
ENABLE_TRACING=true
LOG_LEVEL=info
ENABLE_CORRELATION_IDS=true
ENABLE_REQUEST_LOGGING=true
METRICS_ENDPOINT_ENABLED=true

# Logging
RUST_LOG=invoice_generator_api=info,tower_http=info
```

### Environment-Specific Configurations

#### Development

```env
LOG_LEVEL=debug
RUST_LOG=debug
ENABLE_REQUEST_LOGGING=true
METRICS_ENDPOINT_ENABLED=true
```

#### Staging

```env
LOG_LEVEL=info
RUST_LOG=info
ENABLE_METRICS=true
ENABLE_TRACING=true
```

#### Production

```env
LOG_LEVEL=warn
RUST_LOG=warn
ENABLE_HSTS=true
ENABLE_CSP=true
ENABLE_SECURITY_HEADERS=true
REQUEST_TIMEOUT_SECONDS=30
```

## Database Setup

### PostgreSQL Installation and Configuration

#### Local PostgreSQL Setup

1. **Install PostgreSQL**:

   ```bash
   # macOS with Homebrew
   brew install postgresql@15
   brew services start postgresql@15

   # Ubuntu/Debian
   sudo apt update
   sudo apt install postgresql-15 postgresql-contrib-15
   sudo systemctl start postgresql
   sudo systemctl enable postgresql
   ```

2. **Create database and user**:

   ```bash
   sudo -u postgres psql
   ```

   ```sql
   CREATE DATABASE invoice_generator;
   CREATE USER invoice_user WITH PASSWORD 'secure_password';
   GRANT ALL PRIVILEGES ON DATABASE invoice_generator TO invoice_user;
   \q
   ```

3. **Configure PostgreSQL** (`postgresql.conf`):

   ```ini
   # Connection settings
   listen_addresses = 'localhost'
   port = 5432
   max_connections = 200

   # Memory settings
   shared_buffers = 256MB
   effective_cache_size = 1GB
   work_mem = 4MB
   maintenance_work_mem = 64MB
   ```

#### Managed Database Services

##### AWS RDS

```bash
aws rds create-db-instance \
  --db-instance-identifier invoice-generator-db \
  --db-instance-class db.t3.micro \
  --engine postgres \
  --engine-version 15.4 \
  --master-username postgres \
  --master-user-password your-secure-password \
  --allocated-storage 20 \
  --storage-type gp2 \
  --backup-retention-period 7 \
  --multi-az \
  --storage-encrypted
```

### Database Migrations

The application automatically runs migrations on startup. For manual migration management:

```bash
# Run migrations
cargo run -- migrate

# Check migration status
cargo run -- migrate status

# Rollback migrations
cargo run -- migrate rollback --steps 1
```

## Cloudflare R2 Setup

### Account Setup

1. **Create Cloudflare account** at https://cloudflare.com
2. **Navigate to R2 Object Storage** in the dashboard
3. **Create a new bucket**:
   - Name: `invoice-pdfs` (or your preferred name)
   - Location: Choose based on your users' location

### API Token Creation

1. **Go to "Manage R2 API Tokens"**
2. **Create Custom Token**:

   - Token name: `invoice-generator-api`
   - Permissions: `Object Read and Write`
   - Account resources: Include your account
   - Bucket resources: Include your bucket

3. **Note the credentials**:
   - Access Key ID
   - Secret Access Key
   - Endpoint URL format: `https://<account-id>.r2.cloudflarestorage.com`

### Testing R2 Connection

```bash
# Test with curl
curl -X PUT \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/pdf" \
  --data-binary "@test.pdf" \
  "https://<account-id>.r2.cloudflarestorage.com/invoice-pdfs/test.pdf"
```

## Monitoring and Logging

### Prometheus Metrics

Enable metrics collection:

```env
ENABLE_METRICS=true
METRICS_ENDPOINT_ENABLED=true
```

Access metrics at: `http://localhost:3000/metrics`

### Structured Logging Configuration

```env
ENABLE_TRACING=true
LOG_LEVEL=info
ENABLE_CORRELATION_IDS=true
ENABLE_REQUEST_LOGGING=true
RUST_LOG=invoice_generator_api=info,tower_http=info
```

## Troubleshooting

### Common Issues

#### 1. Database Connection Failed

```
Error: Failed to connect to database
```

**Solutions**:

- Check DATABASE_URL format
- Verify PostgreSQL is running
- Check firewall settings
- Verify credentials

```bash
# Test database connection
psql "postgresql://user:pass@host:port/database"
```

#### 2. R2 Storage Errors

```
Error: Failed to upload to R2 storage
```

**Solutions**:

- Verify R2 credentials
- Check bucket exists and is accessible
- Verify endpoint URL format
- Check network connectivity

#### 3. PDF Generation Timeout

```
Error: PDF generation timeout
```

**Solutions**:

- Increase timeout settings
- Check system resources
- Verify template syntax
- Check for memory leaks

### Health Checks

#### Application Health

```bash
curl http://localhost:3000/health
```

#### Database Health

```bash
curl http://localhost:3000/health | jq '.checks.database'
```

#### Storage Health

```bash
curl http://localhost:3000/health | jq '.checks.storage'
```

For additional support, check the [main documentation](../README.md) or open an issue on GitHub.
