# Database Schema Documentation

## Overview

The Invoice Generator API uses PostgreSQL as its primary database with Sea ORM for data access. The database schema is designed to support invoice generation, audit trails, and rate limiting functionality.

## Database Tables

### 1. invoices

The main table storing invoice metadata and information.

```sql
CREATE TABLE invoices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    template_id VARCHAR(50) NULL,
    invoice_date DATE NOT NULL,
    due_date DATE NULL,
    currency VARCHAR(3) NOT NULL,
    total_amount DECIMAL(10, 2) NOT NULL,
    tax_amount DECIMAL(10, 2) NULL,
    gst_amount DECIMAL(10, 2) NULL,
    shipping_fee DECIMAL(10, 2) NULL,
    discount_amount DECIMAL(10, 2) NULL,
    billed_to_name VARCHAR(255) NOT NULL,
    billed_to_email VARCHAR(255) NULL,
    from_name VA<PERSON>HA<PERSON>(255) NOT NULL,
    from_email VARCHAR(255) NULL,
    file_path VARCHAR(255) NULL,
    file_size BIGINT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    ip_address INET NOT NULL
);
```

**Fields:**

- `id`: Unique identifier for the invoice (UUID)
- `template_id`: Template used for PDF generation (nullable for default template)
- `invoice_date`: Date when the invoice was issued
- `due_date`: Payment due date (optional)
- `currency`: ISO 4217 currency code (3 characters)
- `total_amount`: Total invoice amount including all fees and taxes
- `tax_amount`: Tax amount (if applicable)
- `gst_amount`: GST amount (if applicable)
- `shipping_fee`: Shipping fee (if applicable)
- `discount_amount`: Discount amount (if applicable)
- `billed_to_name`: Name of the invoice recipient
- `billed_to_email`: Email of the invoice recipient (optional)
- `from_name`: Name of the invoice sender
- `from_email`: Email of the invoice sender (optional)
- `file_path`: Path to the PDF file in R2 storage (set after upload)
- `file_size`: Size of the PDF file in bytes
- `created_at`: Timestamp when the invoice was created
- `expires_at`: Timestamp when the invoice and PDF will expire (7 days default)
- `ip_address`: IP address of the client who created the invoice

### 2. invoice_items

Table storing individual line items for each invoice.

```sql
CREATE TABLE invoice_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    invoice_id UUID NOT NULL REFERENCES invoices(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT NULL,
    quantity INTEGER NOT NULL,
    price DECIMAL(10, 2) NOT NULL,
    total DECIMAL(10, 2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);
```

**Fields:**

- `id`: Unique identifier for the invoice item (UUID)
- `invoice_id`: Foreign key referencing the parent invoice
- `name`: Name/title of the item or service
- `description`: Detailed description of the item (optional)
- `quantity`: Quantity of the item
- `price`: Unit price of the item
- `total`: Total amount for this line item (quantity × price)
- `created_at`: Timestamp when the item was created

**Relationships:**

- `invoice_items.invoice_id` → `invoices.id` (CASCADE DELETE)

### 3. rate_limits

Table for tracking API rate limits per IP address.

```sql
CREATE TABLE rate_limits (
    ip_address INET PRIMARY KEY,
    request_count INTEGER NOT NULL DEFAULT 0,
    window_start TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    last_request TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);
```

**Fields:**

- `ip_address`: IP address of the client (primary key)
- `request_count`: Number of requests made in the current window
- `window_start`: Start time of the current rate limiting window
- `last_request`: Timestamp of the most recent request

## Database Indexes

The following indexes are created for optimal query performance:

```sql
-- Performance indexes for common queries
CREATE INDEX idx_invoices_expires_at ON invoices(expires_at);
CREATE INDEX idx_invoices_created_at ON invoices(created_at);
CREATE INDEX idx_invoices_template_id ON invoices(template_id);
CREATE INDEX idx_invoice_items_invoice_id ON invoice_items(invoice_id);
CREATE INDEX idx_rate_limits_window_start ON rate_limits(window_start);
```

**Index Usage:**

- `idx_invoices_expires_at`: Used for cleanup operations to find expired invoices
- `idx_invoices_created_at`: Used for sorting and filtering invoices by creation date
- `idx_invoices_template_id`: Used for template-based queries and analytics
- `idx_invoice_items_invoice_id`: Used for joining invoice items with invoices
- `idx_rate_limits_window_start`: Used for rate limit cleanup operations

## Entity Relationships

```
invoices (1) ←→ (many) invoice_items
```

- One invoice can have multiple invoice items
- Invoice items are automatically deleted when the parent invoice is deleted (CASCADE)
- Rate limits are independent and tracked per IP address

## Repository Pattern Usage

### InvoiceRepository

```rust
use crate::repositories::{InvoiceRepository, CreateInvoiceData};

// Create a new invoice
let invoice_data = CreateInvoiceData {
    template_id: Some("default".to_string()),
    invoice_date: chrono::Utc::now().date_naive(),
    due_date: None,
    currency: "USD".to_string(),
    total_amount: rust_decimal::Decimal::from_str("100.00").unwrap(),
    // ... other fields
};

let invoice = InvoiceRepository::create(db, invoice_data).await?;

// Find invoice by ID
let invoice = InvoiceRepository::find_by_id(db, invoice_id).await?;

// Find expired invoices for cleanup
let expired = InvoiceRepository::find_expired(db, 100).await?;

// Update file path after PDF upload
InvoiceRepository::update_file_path(db, invoice_id, "path/to/file.pdf", Some(1024)).await?;
```

### InvoiceItemRepository

```rust
use crate::repositories::{InvoiceItemRepository, CreateInvoiceItemData};

// Create invoice items
let items_data = vec![
    CreateInvoiceItemData {
        invoice_id,
        name: "Web Development".to_string(),
        description: Some("Frontend development work".to_string()),
        quantity: 40,
        price: rust_decimal::Decimal::from_str("75.00").unwrap(),
    },
    // ... more items
];

let items = InvoiceItemRepository::create_batch(db, items_data).await?;

// Get all items for an invoice
let items = InvoiceItemRepository::find_by_invoice_id(db, invoice_id).await?;

// Calculate total for an invoice
let total = InvoiceItemRepository::calculate_invoice_total(db, invoice_id).await?;
```

### RateLimitRepository

```rust
use crate::repositories::RateLimitRepository;

// Check if IP is rate limited
let is_limited = RateLimitRepository::is_rate_limited(
    db,
    "***********",
    5,  // max requests
    1   // window in minutes
).await?;

// Increment request count
let status = RateLimitRepository::increment_request_count(
    db,
    "***********",
    1   // window in minutes
).await?;

// Get remaining requests
let remaining = RateLimitRepository::get_remaining_requests(
    db,
    "***********",
    5,  // max requests
    1   // window in minutes
).await?;

// Cleanup expired rate limit records
let deleted = RateLimitRepository::cleanup_expired(db, 60).await?; // 60 minutes
```

## Migration Management

### Running Migrations

```rust
use crate::database::Database;

// Initialize database and run migrations
let database = Database::new(&database_url, 100, 5, 8).await?;
database.migrate().await?;
```

### Available Migration Commands

```rust
// Run all pending migrations
database.migrate().await?;

// Rollback migrations (specify number of steps)
database.rollback(Some(1)).await?;

// Fresh database (drop all tables and recreate)
database.refresh().await?;
```

### Adding New Migrations

1. Create a new migration file in `src/database/migrations/`
2. Follow the naming convention: `m{YYYYMMDD}_{HHMMSS}_{description}.rs`
3. Add the migration to the `Migrator` in `src/database/migrations.rs`

Example migration structure:

```rust
use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        // Migration logic here
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        // Rollback logic here
    }
}
```

## Performance Considerations

### Query Optimization

1. **Use indexes**: All common query patterns have supporting indexes
2. **Limit results**: Use pagination for large result sets
3. **Batch operations**: Use transactions for multiple related operations
4. **Connection pooling**: Configured with appropriate pool sizes

### Cleanup Operations

1. **Expired invoices**: Run cleanup daily to remove expired invoice records
2. **Rate limit records**: Clean up expired rate limit entries hourly
3. **File cleanup**: Coordinate with R2 storage cleanup

### Example Cleanup Job

```rust
use crate::repositories::{InvoiceRepository, RateLimitRepository};

// Daily cleanup of expired invoices
let deleted_invoices = InvoiceRepository::delete_expired_batch(db, 1000).await?;
println!("Deleted {} expired invoices", deleted_invoices);

// Hourly cleanup of expired rate limits
let deleted_rate_limits = RateLimitRepository::cleanup_expired(db, 60).await?;
println!("Deleted {} expired rate limit records", deleted_rate_limits);
```

## Environment Configuration

Database connection settings are configured via environment variables:

```env
DATABASE_URL=postgresql://username:password@localhost:5432/invoice_generator
DATABASE_MAX_CONNECTIONS=100
DATABASE_MIN_CONNECTIONS=5
DATABASE_CONNECT_TIMEOUT=8
```

## Error Handling

All repository methods return `Result<T, DbErr>` for proper error handling:

```rust
match InvoiceRepository::find_by_id(db, invoice_id).await {
    Ok(Some(invoice)) => {
        // Invoice found
        println!("Found invoice: {}", invoice.id);
    },
    Ok(None) => {
        // Invoice not found
        return Err("Invoice not found".into());
    },
    Err(db_err) => {
        // Database error
        return Err(format!("Database error: {}", db_err).into());
    }
}
```

## Testing

### Unit Tests

Repository methods should be tested with a test database:

```rust
#[cfg(test)]
mod tests {
    use super::*;
    use sea_orm::Database;

    async fn setup_test_db() -> DatabaseConnection {
        Database::connect("sqlite::memory:").await.unwrap()
    }

    #[tokio::test]
    async fn test_create_invoice() {
        let db = setup_test_db().await;
        // Test implementation
    }
}
```

### Integration Tests

Test complete workflows with a dedicated test database to ensure all components work together correctly.

## Security Considerations

1. **SQL Injection**: Sea ORM uses parameterized queries automatically
2. **Connection Security**: Use TLS for database connections in production
3. **Data Retention**: Automatic cleanup of expired data
4. **Rate Limiting**: IP-based rate limiting to prevent abuse
5. **Input Validation**: Validate all input data before database operations

## Monitoring

Monitor these key metrics:

1. **Connection pool usage**: Track active/idle connections
2. **Query performance**: Monitor slow queries
3. **Storage growth**: Track database size and growth rate
4. **Error rates**: Monitor database connection and query errors
5. **Cleanup effectiveness**: Track cleanup job success rates
