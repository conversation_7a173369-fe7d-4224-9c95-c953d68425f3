# API Reference

This document provides comprehensive documentation for the Invoice Generator API endpoints, request/response formats, and error handling.

## Base URL

```
http://localhost:3000  # Development
https://your-domain.com  # Production
```

## Authentication

No authentication is required. The API is designed for public use with rate limiting to ensure fair usage.

## Rate Limiting

- **Limit**: 5 requests per minute per IP address
- **Algorithm**: Sliding window
- **Headers**: Rate limit information is included in response headers

### Rate Limit Headers

```http
X-RateLimit-Limit: 5
X-RateLimit-Remaining: 4
X-RateLimit-Reset: 1640995200
```

## Content Type

All API endpoints accept and return JSON data:

```http
Content-Type: application/json
Accept: application/json
```

## Endpoints

### 1. Generate Invoice

Generate a new PDF invoice from the provided data.

**Endpoint**: `POST /api/v1/invoices`

#### Request Body

```json
{
  "template_id": "default",
  "invoice_date": "2024-01-15",
  "due_date": "2024-02-15",
  "currency": "USD",
  "tax": 10.0,
  "gst": 5.0,
  "shipping_fee": 25.0,
  "discount": {
    "percentage": 10.0,
    "amount": 100.0
  },
  "billed_to": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "address": {
      "street": "123 Main St",
      "city": "New York",
      "state": "NY",
      "post_code": "10001",
      "country": "USA"
    }
  },
  "from": {
    "name": "Your Company",
    "email": "<EMAIL>",
    "phone": "+1987654321",
    "address": {
      "street": "456 Business Ave",
      "city": "San Francisco",
      "state": "CA",
      "post_code": "94105",
      "country": "USA"
    }
  },
  "items": [
    {
      "name": "Web Development",
      "description": "Frontend development services",
      "quantity": 40,
      "price": 75.0
    },
    {
      "name": "Hosting Setup",
      "description": "Server configuration and deployment",
      "quantity": 1,
      "price": 200.0
    }
  ]
}
```

#### Field Descriptions

| Field                 | Type          | Required | Description                                 |
| --------------------- | ------------- | -------- | ------------------------------------------- |
| `template_id`         | string        | No       | Template identifier (default: "default")    |
| `invoice_date`        | string (date) | Yes      | Invoice issue date (YYYY-MM-DD)             |
| `due_date`            | string (date) | No       | Payment due date (YYYY-MM-DD)               |
| `currency`            | string        | Yes      | ISO 4217 currency code (e.g., "USD", "EUR") |
| `tax`                 | number        | No       | Tax percentage (0-100)                      |
| `gst`                 | number        | No       | GST percentage (0-100)                      |
| `shipping_fee`        | number        | No       | Shipping fee amount                         |
| `discount`            | object        | No       | Discount information                        |
| `discount.percentage` | number        | No       | Discount percentage (0-100)                 |
| `discount.amount`     | number        | No       | Fixed discount amount                       |
| `billed_to`           | object        | Yes      | Invoice recipient information               |
| `from`                | object        | Yes      | Invoice sender information                  |
| `items`               | array         | Yes      | Invoice line items (min: 1 item)            |

#### Contact Object

| Field     | Type   | Required | Description                   |
| --------- | ------ | -------- | ----------------------------- |
| `name`    | string | Yes      | Contact name (max: 255 chars) |
| `email`   | string | No       | Email address (valid format)  |
| `phone`   | string | No       | Phone number                  |
| `address` | object | No       | Address information           |

#### Address Object

| Field       | Type   | Required | Description     |
| ----------- | ------ | -------- | --------------- |
| `street`    | string | No       | Street address  |
| `city`      | string | No       | City name       |
| `state`     | string | No       | State/province  |
| `post_code` | string | No       | Postal/ZIP code |
| `country`   | string | No       | Country name    |

#### Invoice Item Object

| Field         | Type    | Required | Description                |
| ------------- | ------- | -------- | -------------------------- |
| `name`        | string  | Yes      | Item name (max: 255 chars) |
| `description` | string  | No       | Item description           |
| `quantity`    | integer | Yes      | Quantity (min: 1)          |
| `price`       | number  | Yes      | Unit price (min: 0)        |

#### Success Response (200 OK)

```json
{
  "success": true,
  "data": {
    "invoice_id": "550e8400-e29b-41d4-a716-************",
    "download_url": "https://r2.cloudflarestorage.com/invoice-pdfs/...",
    "expires_at": "2024-01-22T12:00:00Z",
    "file_size": 245760,
    "created_at": "2024-01-15T12:00:00Z",
    "total_amount": 3200.0,
    "currency": "USD"
  }
}
```

#### Response Fields

| Field          | Type              | Description                     |
| -------------- | ----------------- | ------------------------------- |
| `invoice_id`   | string (UUID)     | Unique invoice identifier       |
| `download_url` | string (URL)      | Pre-signed URL for PDF download |
| `expires_at`   | string (ISO 8601) | URL expiration timestamp        |
| `file_size`    | integer           | PDF file size in bytes          |
| `created_at`   | string (ISO 8601) | Invoice creation timestamp      |
| `total_amount` | number            | Total invoice amount            |
| `currency`     | string            | Currency code                   |

### 2. Health Check

Check the service health and status.

**Endpoint**: `GET /health`

#### Success Response (200 OK)

```json
{
  "status": "healthy",
  "service": "invoice-generator-api",
  "version": "0.1.0",
  "timestamp": "2024-01-15T12:00:00Z",
  "checks": {
    "database": "healthy",
    "storage": "healthy"
  }
}
```

### 3. List Templates

Get information about available invoice templates.

**Endpoint**: `GET /api/v1/templates`

#### Success Response (200 OK)

```json
{
  "success": true,
  "data": {
    "templates": [
      {
        "id": "default",
        "name": "Default Template",
        "description": "Standard professional invoice template",
        "preview_url": null,
        "features": [
          "Professional layout",
          "Multi-page support",
          "Tax calculations",
          "Discount support"
        ]
      }
    ]
  }
}
```

### 4. Metrics (Optional)

Get Prometheus metrics for monitoring.

**Endpoint**: `GET /metrics`

**Note**: Only available if `METRICS_ENDPOINT_ENABLED=true`

#### Success Response (200 OK)

Returns Prometheus-formatted metrics:

````
# HELP http_requests_total Total number of HTTP requests
# TYPE http_requests_total counter
http_requests_total{method="POST",endpoint="/api/v1/invoices",status="200"} 42

# HELP pdf_generation_duration_seconds PDF generation duration
# TYPE pdf_generation_duration_seconds histogram
pdf_generation_duration_seconds_bucket{status="success",le="1"} 35
pdf_generation_duration_seconds_bucket{status="success",le="2"} 40
pdf_generation_duration_seconds_bucket{status="success",le="5"} 42

## Error Handling

The API uses standard HTTP status codes and returns detailed error information in JSON format.

### Error Response Format

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request data",
    "details": [
      {
        "field": "invoice_date",
        "message": "Invalid date format. Expected YYYY-MM-DD"
      },
      {
        "field": "items",
        "message": "At least one item is required"
      }
    ],
    "timestamp": "2024-01-15T12:00:00Z",
    "request_id": "550e8400-e29b-41d4-a716-************"
  }
}
````

### HTTP Status Codes

| Status Code                 | Description         | When It Occurs                        |
| --------------------------- | ------------------- | ------------------------------------- |
| `200 OK`                    | Success             | Request completed successfully        |
| `400 Bad Request`           | Invalid request     | Validation errors, malformed JSON     |
| `429 Too Many Requests`     | Rate limit exceeded | More than 5 requests per minute       |
| `500 Internal Server Error` | Server error        | PDF generation failed, storage issues |
| `503 Service Unavailable`   | Service degraded    | Database or storage unavailable       |

### Error Codes

| Error Code              | Description                  | HTTP Status |
| ----------------------- | ---------------------------- | ----------- |
| `VALIDATION_ERROR`      | Request validation failed    | 400         |
| `RATE_LIMIT_EXCEEDED`   | Too many requests            | 429         |
| `PDF_GENERATION_FAILED` | PDF creation failed          | 500         |
| `STORAGE_ERROR`         | File storage failed          | 500         |
| `DATABASE_ERROR`        | Database operation failed    | 500         |
| `TEMPLATE_ERROR`        | Template rendering failed    | 500         |
| `SERVICE_UNAVAILABLE`   | External service unavailable | 503         |

### Common Error Scenarios

#### 1. Validation Errors (400)

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request data",
    "details": [
      {
        "field": "currency",
        "message": "Invalid currency code. Must be a valid ISO 4217 code"
      },
      {
        "field": "items[0].quantity",
        "message": "Quantity must be greater than 0"
      }
    ]
  }
}
```

#### 2. Rate Limit Exceeded (429)

```json
{
  "success": false,
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Rate limit exceeded. Maximum 5 requests per minute per IP",
    "retry_after": 45
  }
}
```

#### 3. PDF Generation Failed (500)

```json
{
  "success": false,
  "error": {
    "code": "PDF_GENERATION_FAILED",
    "message": "Failed to generate PDF from template",
    "details": "Template rendering timeout"
  }
}
```

#### 4. Storage Error (500)

```json
{
  "success": false,
  "error": {
    "code": "STORAGE_ERROR",
    "message": "Failed to upload PDF to storage",
    "details": "R2 storage service unavailable"
  }
}
```

## Request Examples

### cURL Examples

#### Generate Invoice

```bash
curl -X POST http://localhost:3000/api/v1/invoices \
  -H "Content-Type: application/json" \
  -d '{
    "invoice_date": "2024-01-15",
    "currency": "USD",
    "billed_to": {
      "name": "John Doe",
      "email": "<EMAIL>"
    },
    "from": {
      "name": "Your Company",
      "email": "<EMAIL>"
    },
    "items": [
      {
        "name": "Consulting",
        "quantity": 10,
        "price": 100.0
      }
    ]
  }'
```

#### Health Check

```bash
curl http://localhost:3000/health
```

#### List Templates

```bash
curl http://localhost:3000/api/v1/templates
```

### JavaScript/Fetch Example

```javascript
const generateInvoice = async (invoiceData) => {
  try {
    const response = await fetch("/api/v1/invoices", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(invoiceData),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error.message);
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error("Invoice generation failed:", error);
    throw error;
  }
};

// Usage
const invoice = await generateInvoice({
  invoice_date: "2024-01-15",
  currency: "USD",
  billed_to: {
    name: "John Doe",
    email: "<EMAIL>",
  },
  from: {
    name: "Your Company",
    email: "<EMAIL>",
  },
  items: [
    {
      name: "Web Development",
      quantity: 40,
      price: 75.0,
    },
  ],
});

console.log("Invoice generated:", invoice.download_url);
```

### Python/Requests Example

```python
import requests
import json

def generate_invoice(invoice_data):
    url = 'http://localhost:3000/api/v1/invoices'
    headers = {'Content-Type': 'application/json'}

    try:
        response = requests.post(url, headers=headers, json=invoice_data)
        response.raise_for_status()
        return response.json()['data']
    except requests.exceptions.HTTPError as e:
        error_data = response.json()
        raise Exception(f"API Error: {error_data['error']['message']}")

# Usage
invoice_data = {
    'invoice_date': '2024-01-15',
    'currency': 'USD',
    'billed_to': {
        'name': 'John Doe',
        'email': '<EMAIL>'
    },
    'from': {
        'name': 'Your Company',
        'email': '<EMAIL>'
    },
    'items': [
        {
            'name': 'Consulting',
            'quantity': 10,
            'price': 100.0
        }
    ]
}

try:
    invoice = generate_invoice(invoice_data)
    print(f"Invoice generated: {invoice['download_url']}")
except Exception as e:
    print(f"Error: {e}")
```

## Best Practices

### 1. Error Handling

Always check the `success` field in responses and handle errors appropriately:

```javascript
if (response.success) {
  // Handle success
  const downloadUrl = response.data.download_url;
} else {
  // Handle error
  console.error("Error:", response.error.message);
  if (response.error.details) {
    response.error.details.forEach((detail) => {
      console.error(`Field ${detail.field}: ${detail.message}`);
    });
  }
}
```

### 2. Rate Limiting

Implement retry logic with exponential backoff for rate limit errors:

```javascript
const retryWithBackoff = async (fn, maxRetries = 3) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      if (error.status === 429 && i < maxRetries - 1) {
        const delay = Math.pow(2, i) * 1000; // Exponential backoff
        await new Promise((resolve) => setTimeout(resolve, delay));
        continue;
      }
      throw error;
    }
  }
};
```

### 3. Input Validation

Validate data on the client side before sending requests:

```javascript
const validateInvoiceData = (data) => {
  const errors = [];

  if (!data.invoice_date) {
    errors.push("Invoice date is required");
  }

  if (!data.currency || data.currency.length !== 3) {
    errors.push("Valid currency code is required");
  }

  if (!data.items || data.items.length === 0) {
    errors.push("At least one item is required");
  }

  return errors;
};
```

### 4. File Download

Handle PDF downloads properly:

```javascript
const downloadPdf = async (downloadUrl, filename = "invoice.pdf") => {
  try {
    const response = await fetch(downloadUrl);
    const blob = await response.blob();

    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error("Download failed:", error);
  }
};
```

## Security Considerations

1. **Input Sanitization**: All input is automatically sanitized server-side
2. **Rate Limiting**: Prevents abuse with IP-based rate limiting
3. **HTTPS**: Use HTTPS in production for secure data transmission
4. **File Access**: PDF URLs expire after 7 days for security
5. **No Authentication**: No sensitive data is stored or required

## Support

For API support and questions:

- Check the [documentation](../README.md)
- Review [common issues](../docs/)
- Open an issue on GitHub
- Check server logs for detailed error information

```

```
