# Cloudflare R2 Storage Configuration

This document outlines how to configure and use Cloudflare R2 storage for PDF file management in the Invoice Generator API.

## Overview

The Invoice Generator API uses Cloudflare R2 (S3-compatible storage) to store generated PDF invoices. R2 provides:

- Cost-effective object storage
- S3-compatible API
- Global edge locations
- No egress fees
- Automatic cleanup of expired files

## Configuration

### Environment Variables

Add the following environment variables to your `.env` file:

```bash
# Cloudflare R2 Storage Configuration
R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
R2_ACCESS_KEY=your-r2-access-key-here
R2_SECRET_KEY=your-r2-secret-key-here
R2_BUCKET_NAME=invoice-pdfs
R2_REGION=auto

# PDF Configuration
PDF_RETENTION_DAYS=7
```

### Setting Up Cloudflare R2

1. **Create an R2 Account**:

   - Log into Cloudflare Dashboard
   - Navigate to R2 Object Storage
   - Create a new R2 bucket (e.g., `invoice-pdfs`)

2. **Generate API Tokens**:

   - Go to "Manage R2 API Tokens"
   - Create a new API token with R2 permissions
   - Note down the Access Key and Secret Key

3. **Configure Endpoint**:
   - Find your account ID in the Cloudflare dashboard
   - Set R2_ENDPOINT to `https://{account-id}.r2.cloudflarestorage.com`

## Features

### File Upload

- Automatic PDF upload to R2 storage
- Organized file structure: `invoices/YYYY/MM/DD/uuid.pdf`
- Proper content-type headers for PDF files
- Error handling and retry logic

### Pre-signed URLs

- Secure, temporary access to PDF files
- 7-day expiration by default
- No credential exposure
- Direct browser downloads

### Automatic Cleanup

- Files older than `PDF_RETENTION_DAYS` are automatically deleted
- Configurable retention period
- Batch deletion for efficiency
- Comprehensive logging

### Health Checks

- Automatic R2 connectivity testing on startup
- Bucket accessibility verification
- Graceful degradation if R2 is unavailable

## Usage

### In Application Code

The `StorageService` is automatically initialized and available in the application state:

```rust
use crate::services::storage::StorageService;

// Upload a PDF
let file_key = storage_service.generate_file_key(&invoice_id);
let result = storage_service.upload_pdf(pdf_data, file_key).await?;

// Generate download URL
let download_url = storage_service.generate_presigned_url(&file_key).await?;

// Delete a file
storage_service.delete_file(&file_key).await?;

// Cleanup expired files
let deleted_files = storage_service.delete_expired_files(7).await?;
```

### Error Handling

The service uses a comprehensive error system:

```rust
use crate::services::storage::{StorageError, StorageResult};

match storage_service.upload_pdf(data, key).await {
    Ok(file_key) => {
        // Success
    }
    Err(StorageError::Configuration(msg)) => {
        // Configuration error
    }
    Err(StorageError::Upload(msg)) => {
        // Upload failed
    }
    Err(StorageError::PresignedUrl(msg)) => {
        // URL generation failed
    }
    Err(StorageError::Delete(msg)) => {
        // Deletion failed
    }
}
```

## File Organization

Files are stored with the following structure:

```
bucket-name/
├── invoices/
│   ├── 2024/
│   │   ├── 01/
│   │   │   ├── 15/
│   │   │   │   ├── uuid1.pdf
│   │   │   │   └── uuid2.pdf
│   │   │   └── 16/
│   │   │       └── uuid3.pdf
│   │   └── 02/
│   │       └── ...
│   └── 2025/
│       └── ...
```

This organization provides:

- Easy browsing by date
- Efficient cleanup operations
- Logical file grouping
- Scalable structure

## Monitoring and Logging

The service provides comprehensive logging:

- **Info Level**: Successful operations, initialization
- **Warn Level**: Non-critical errors, degraded functionality
- **Error Level**: Critical failures, configuration issues

Key metrics to monitor:

- Upload success/failure rates
- Pre-signed URL generation
- Cleanup operation results
- Health check status

## Security Considerations

- **Credentials**: Store R2 credentials securely in environment variables
- **Access Control**: Use minimal required permissions for R2 API tokens
- **Pre-signed URLs**: URLs expire automatically after 7 days
- **Bucket Access**: Configure bucket policies to restrict access
- **Audit Trail**: All operations are logged for security monitoring

## Troubleshooting

### Common Issues

1. **Configuration Errors**:

   - Verify R2_ENDPOINT format
   - Check access key and secret key
   - Ensure bucket exists and is accessible

2. **Upload Failures**:

   - Check bucket permissions
   - Verify file size limits
   - Monitor network connectivity

3. **Health Check Failures**:
   - Application continues to run but R2 features are disabled
   - Check R2 service status
   - Verify credentials and bucket access

### Debug Mode

Enable debug logging to troubleshoot issues:

```bash
RUST_LOG=invoice_generator_api=debug,tower_http=debug
```

This will provide detailed information about R2 operations and help identify configuration or connectivity issues.
