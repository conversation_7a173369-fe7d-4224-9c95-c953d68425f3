# Scheduled Cleanup Tasks

This document describes the automated cleanup tasks implemented in the Invoice Generator API to maintain system health and manage storage efficiently.

## Overview

The application includes background tasks that automatically clean up expired data to prevent storage bloat and maintain optimal performance. These tasks run on scheduled intervals and handle:

1. **Expired Invoices**: Remove invoice metadata from the database after the retention period
2. **Expired PDF Files**: Delete PDF files from Cloudflare R2 storage after the retention period  
3. **Expired Rate Limit Entries**: Clean up old rate limiting records to prevent memory bloat

## Task Scheduler

The `TaskScheduler` manages all background cleanup tasks and is automatically started when the application boots.

### Architecture

```
TaskScheduler
├── Daily Cleanup (runs every 24 hours)
│   ├── Delete expired invoices from database
│   └── Delete expired PDF files from R2 storage
└── Hourly Cleanup (runs every hour)
    └── Delete expired rate limit entries
```

### Configuration

Cleanup behavior is controlled by environment variables:

- `PDF_RETENTION_DAYS`: How long to keep PDF files (default: 7 days)
- `RATE_LIMIT_WINDOW_SECONDS`: Rate limit window duration (default: 60 minutes)

## Cleanup Tasks

### Daily Cleanup

**Schedule**: Every 24 hours  
**Initial Delay**: 30 seconds after application start

#### Expired Invoices
- Removes invoice records where `expires_at < NOW()`
- Uses batch processing (1000 records per batch) to avoid database overload
- Automatically cascades to delete related invoice items
- Logs the number of deleted records

#### Expired PDF Files
- Scans Cloudflare R2 bucket for files older than `PDF_RETENTION_DAYS`
- Deletes files based on their last modified timestamp
- Logs the list of deleted file keys
- Handles R2 API errors gracefully

### Hourly Cleanup

**Schedule**: Every hour  
**Initial Delay**: 60 seconds after application start

#### Expired Rate Limit Entries
- Removes rate limit records where `window_start < (NOW() - window_duration)`
- Prevents the rate_limits table from growing indefinitely
- Logs the number of deleted records

## Error Handling

The cleanup tasks are designed to be resilient:

- **Database Errors**: Logged but don't crash the application
- **Storage Errors**: Logged but don't crash the application  
- **Partial Failures**: Daily cleanup continues even if one task fails
- **Missed Ticks**: Uses `MissedTickBehavior::Skip` to prevent task buildup

## Monitoring

All cleanup operations are logged with appropriate levels:

- `INFO`: Successful operations with counts
- `WARN`: Partial failures or configuration issues
- `ERROR`: Complete task failures

### Example Log Output

```
2024-01-15T10:00:00Z INFO Starting daily cleanup tasks
2024-01-15T10:00:01Z INFO Daily cleanup: Deleted 42 expired invoices
2024-01-15T10:00:02Z INFO Daily cleanup: Deleted 15 expired PDF files
2024-01-15T10:00:02Z INFO Daily cleanup completed successfully

2024-01-15T11:00:00Z INFO Starting hourly cleanup tasks  
2024-01-15T11:00:01Z INFO Hourly cleanup: Deleted 128 expired rate limit entries
2024-01-15T11:00:01Z INFO Hourly cleanup completed successfully
```

## Manual Cleanup

For testing or maintenance purposes, cleanup tasks can be triggered manually:

```rust
// Get the scheduler from application state
let scheduler = &app_state.scheduler;

// Run immediate cleanup (both daily and hourly tasks)
scheduler.run_immediate_cleanup().await?;
```

## Performance Considerations

- **Batch Processing**: Invoice deletion uses batches to avoid long-running transactions
- **Non-blocking**: All cleanup tasks run in background threads
- **Resource Limits**: Tasks are designed to have minimal impact on API performance
- **Timing**: Tasks run during off-peak hours when possible

## Database Impact

The cleanup tasks interact with the following database tables:

- `invoices`: Deletes expired records (cascades to `invoice_items`)
- `rate_limits`: Deletes expired rate limiting entries

Indexes on `expires_at` and `window_start` columns ensure efficient cleanup queries.

## Storage Impact

R2 storage cleanup:
- Lists objects in the bucket (paginated for large buckets)
- Filters by last modified date
- Deletes expired files individually
- Handles eventual consistency in R2

## Testing

Basic unit tests verify:
- Task creation and configuration
- Config validation
- Error handling structure

Integration tests would require:
- Test database with expired data
- Mock R2 storage service
- Time manipulation for scheduler testing

## Troubleshooting

### Common Issues

1. **High Memory Usage**: Check if rate limit cleanup is running properly
2. **Storage Costs**: Verify PDF cleanup is deleting expired files
3. **Database Growth**: Ensure invoice cleanup is processing expired records

### Debugging

Enable debug logging to see detailed cleanup operations:

```bash
RUST_LOG=debug ./invoice-generator-api
```

This will show detailed information about each cleanup operation including SQL queries and R2 API calls.
