# Contributing to Invoice Generator API

Thank you for your interest in contributing to the Invoice Generator API! This document provides guidelines and information for contributors.

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Setup](#development-setup)
- [Contributing Process](#contributing-process)
- [Coding Standards](#coding-standards)
- [Testing](#testing)
- [Documentation](#documentation)
- [Submitting Changes](#submitting-changes)

## Code of Conduct

This project adheres to a Code of Conduct that we expect all contributors to follow. Please read [CODE_OF_CONDUCT.md](CODE_OF_CONDUCT.md) before contributing.

## Getting Started

### Prerequisites

- Rust (latest stable version)
- PostgreSQL 12+
- Docker and Docker Compose
- Git

### Development Setup

1. **Fork and Clone**:
   ```bash
   git clone https://github.com/your-username/invoice-generator-api.git
   cd invoice-generator-api
   ```

2. **Environment Setup**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Database Setup**:
   ```bash
   # Using Docker Compose (recommended)
   docker-compose up -d postgres
   
   # Or manually
   createdb invoice_generator
   ```

4. **Install Dependencies and Run**:
   ```bash
   cargo build
   cargo test
   cargo run
   ```

## Contributing Process

1. **Check Issues**: Look for existing issues or create a new one
2. **Fork & Branch**: Create a feature branch from `main`
3. **Develop**: Make your changes following our coding standards
4. **Test**: Ensure all tests pass and add new tests if needed
5. **Document**: Update documentation as necessary
6. **Submit**: Create a pull request with a clear description

### Branch Naming

Use descriptive branch names:
- `feature/add-template-system`
- `fix/rate-limit-bug`
- `docs/api-documentation`
- `refactor/storage-service`

## Coding Standards

### Rust Guidelines

- Follow the [Rust API Guidelines](https://rust-lang.github.io/api-guidelines/)
- Use `cargo fmt` for consistent formatting
- Use `cargo clippy` to catch common mistakes
- Write comprehensive documentation comments (`///`)
- Use meaningful variable and function names

### Code Style

```rust
// Good: Clear, documented function
/// Generates a pre-signed URL for PDF download
/// 
/// # Arguments
/// * `file_key` - The unique file identifier in R2 storage
/// 
/// # Returns
/// * `Result<String, StorageError>` - The pre-signed URL or error
pub async fn generate_presigned_url(&self, file_key: &str) -> Result<String, StorageError> {
    // Implementation
}

// Good: Proper error handling
match storage_service.upload_pdf(data, key).await {
    Ok(file_key) => info!("PDF uploaded successfully: {}", file_key),
    Err(e) => {
        error!("Failed to upload PDF: {}", e);
        return Err(AppError::Storage(e.to_string()));
    }
}
```

### Project Structure

- `src/handlers/` - HTTP request handlers
- `src/services/` - Business logic services
- `src/repositories/` - Data access layer
- `src/models/` - Data structures and validation
- `src/middleware/` - HTTP middleware
- `src/database/` - Database connection and migrations
- `tests/` - Integration tests
- `docs/` - Documentation files

## Testing

### Running Tests

```bash
# Run all tests
cargo test

# Run specific test
cargo test test_name

# Run with output
cargo test -- --nocapture

# Run integration tests
cargo test --test integration_tests
```

### Test Guidelines

1. **Unit Tests**: Test individual functions and methods
2. **Integration Tests**: Test complete workflows
3. **Test Coverage**: Aim for high test coverage on critical paths
4. **Test Data**: Use realistic but anonymized test data
5. **Cleanup**: Ensure tests clean up after themselves

### Example Test

```rust
#[tokio::test]
async fn test_invoice_creation() {
    let config = create_test_config();
    let db = setup_test_database().await;
    
    let invoice_data = CreateInvoiceData {
        // Test data
    };
    
    let result = InvoiceRepository::create(&db, invoice_data).await;
    assert!(result.is_ok());
    
    let invoice = result.unwrap();
    assert_eq!(invoice.currency, "USD");
    
    // Cleanup
    cleanup_test_data(&db, invoice.id).await;
}
```

## Documentation

### Code Documentation

- Document all public APIs with `///` comments
- Include examples in documentation
- Document error conditions and edge cases
- Keep documentation up-to-date with code changes

### API Documentation

- Update API documentation for endpoint changes
- Include request/response examples
- Document error codes and responses
- Update OpenAPI/Swagger specs if applicable

## Submitting Changes

### Pull Request Guidelines

1. **Clear Title**: Use a descriptive title
2. **Description**: Explain what changes were made and why
3. **Testing**: Describe how the changes were tested
4. **Breaking Changes**: Clearly mark any breaking changes
5. **Documentation**: Update relevant documentation

### Pull Request Template

```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed

## Checklist
- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] Tests added/updated
```

### Review Process

1. **Automated Checks**: CI/CD pipeline runs tests and linting
2. **Code Review**: Maintainers review code for quality and standards
3. **Testing**: Changes are tested in development environment
4. **Approval**: At least one maintainer approval required
5. **Merge**: Changes are merged to main branch

## Issue Reporting

### Bug Reports

Include:
- Clear description of the issue
- Steps to reproduce
- Expected vs actual behavior
- Environment details (OS, Rust version, etc.)
- Relevant logs or error messages

### Feature Requests

Include:
- Clear description of the feature
- Use case and motivation
- Proposed implementation approach
- Potential impact on existing functionality

## Getting Help

- **Documentation**: Check the [docs/](docs/) directory
- **Issues**: Search existing issues or create a new one
- **Discussions**: Use GitHub Discussions for questions
- **Code Review**: Ask for feedback during the PR process

## Recognition

Contributors are recognized in:
- Git commit history
- Release notes for significant contributions
- README.md contributors section

Thank you for contributing to the Invoice Generator API! 🚀
