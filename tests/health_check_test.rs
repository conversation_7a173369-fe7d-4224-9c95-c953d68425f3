use axum::{
    body::Body,
    http::{Request, StatusCode},
};
use serde_json::Value;
use tower::util::ServiceExt;

use invoice_generator_api::{
    config::Config,
    state::AppState,
    handlers::health::health_check,
};

#[tokio::test]
async fn test_health_check_response_structure() {
    // This test verifies the health check response structure
    // Note: This is a unit test for the response format, not integration test
    
    // Load test configuration
    let config = Config::load().expect("Failed to load config for test");
    
    // Try to create app state - this might fail due to database/storage issues in test environment
    // but we can still test the response structure
    match AppState::new(config).await {
        Ok(state) => {
            // If we can create the state, test the actual health check
            let response = health_check(axum::extract::State(state)).await;
            
            match response {
                Ok((status_code, json_response)) => {
                    // Verify we get a valid status code
                    assert!(matches!(
                        status_code,
                        StatusCode::OK | StatusCode::PARTIAL_CONTENT | StatusCode::SERVICE_UNAVAILABLE
                    ));
                    
                    // Verify response structure
                    let health_response = json_response.0;
                    assert_eq!(health_response.service, "invoice-generator-api");
                    assert_eq!(health_response.version, env!("CARGO_PKG_VERSION"));
                    assert!(matches!(
                        health_response.status.as_str(),
                        "healthy" | "degraded" | "unhealthy"
                    ));
                    assert!(matches!(
                        health_response.database.as_str(),
                        "healthy" | "unhealthy"
                    ));
                    assert!(matches!(
                        health_response.storage.as_str(),
                        "healthy" | "degraded" | "unhealthy"
                    ));
                },
                Err(status_code) => {
                    // If health check returns an error status, it should be a server error
                    assert!(status_code.is_server_error());
                }
            }
        },
        Err(_) => {
            // If we can't create app state (e.g., no database), that's expected in test environment
            println!("Skipping integration test - unable to create app state (likely missing database)");
        }
    }
}

#[test]
fn test_determine_overall_status_logic() {
    use invoice_generator_api::handlers::health::determine_overall_status;
    
    // Test all healthy
    assert_eq!(
        determine_overall_status("healthy", "healthy", "healthy"),
        "healthy"
    );
    
    // Test database unhealthy - should be unhealthy
    assert_eq!(
        determine_overall_status("unhealthy", "healthy", "healthy"),
        "unhealthy"
    );
    
    // Test PDF service unhealthy - should be unhealthy
    assert_eq!(
        determine_overall_status("healthy", "healthy", "unhealthy"),
        "unhealthy"
    );
    
    // Test storage degraded - should be degraded
    assert_eq!(
        determine_overall_status("healthy", "degraded", "healthy"),
        "degraded"
    );
    
    // Test multiple issues - database takes precedence
    assert_eq!(
        determine_overall_status("unhealthy", "degraded", "healthy"),
        "unhealthy"
    );
    
    // Test edge cases
    assert_eq!(
        determine_overall_status("healthy", "unhealthy", "healthy"),
        "degraded"  // Storage unhealthy is treated as degraded
    );
}

#[cfg(test)]
mod integration_tests {
    use super::*;
    use axum::{
        routing::get,
        Router,
    };
    
    /// This test requires a running database and will be skipped if not available
    #[tokio::test]
    #[ignore] // Use `cargo test -- --ignored` to run this test
    async fn test_health_endpoint_integration() {
        // Load configuration
        let config = Config::load().expect("Failed to load config");
        
        // Create app state
        let state = AppState::new(config).await.expect("Failed to create app state");
        
        // Create test app
        let app = Router::new()
            .route("/health", get(health_check))
            .with_state(state);
        
        // Create test request
        let request = Request::builder()
            .uri("/health")
            .body(Body::empty())
            .unwrap();
        
        // Send request
        let response = app.oneshot(request).await.unwrap();
        
        // Verify response
        assert!(response.status().is_success() || response.status().is_server_error());
        
        // Parse response body
        let body = axum::body::to_bytes(response.into_body(), usize::MAX).await.unwrap();
        let json: Value = serde_json::from_slice(&body).expect("Response should be valid JSON");
        
        // Verify JSON structure
        assert!(json.get("status").is_some());
        assert!(json.get("service").is_some());
        assert!(json.get("version").is_some());
        assert!(json.get("timestamp").is_some());
        assert!(json.get("database").is_some());
        assert!(json.get("storage").is_some());
        
        // Verify service name
        assert_eq!(json["service"], "invoice-generator-api");
        assert_eq!(json["version"], env!("CARGO_PKG_VERSION"));
    }
}
