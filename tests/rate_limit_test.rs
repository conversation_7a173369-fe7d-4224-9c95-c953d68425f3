use invoice_generator_api::services::rate_limit::RateLimitService;
use sea_orm::{Database, DatabaseConnection};
use sea_orm_migration::MigratorTrait;

/// Create a test database connection using SQLite in-memory
async fn create_test_db() -> DatabaseConnection {
    let db = Database::connect("sqlite::memory:")
        .await
        .expect("Failed to create test database");

    // Run migrations
    invoice_generator_api::database::migrations::Migrator::up(&db, None)
        .await
        .expect("Failed to run migrations");

    db
}

#[tokio::test]
async fn test_rate_limiting_basic() {
    // Create test database connection
    let db = create_test_db().await;

    // Create rate limit service with 3 requests per minute for testing
    let rate_limit_service = RateLimitService::new(
        db,
        3, // max_requests
        1, // window_duration_minutes
    );

    let test_ip = "*************";

    // First 3 requests should be allowed
    for i in 1..=3 {
        let is_limited = rate_limit_service.is_rate_limited(test_ip).await.unwrap();
        assert!(!is_limited, "Request {} should not be rate limited", i);

        let _status = rate_limit_service.increment_request_count(test_ip).await.unwrap();
    }

    // 4th request should be rate limited
    let is_limited = rate_limit_service.is_rate_limited(test_ip).await.unwrap();
    assert!(is_limited, "4th request should be rate limited");

    // Check remaining requests
    let remaining = rate_limit_service.get_remaining_requests(test_ip).await.unwrap();
    assert_eq!(remaining.remaining, 0, "Should have 0 remaining requests");
}

#[tokio::test]
async fn test_rate_limiting_window_reset() {
    // Create test database connection
    let db = create_test_db().await;

    // Create rate limit service with very short window for testing
    let rate_limit_service = RateLimitService::new(
        db,
        2, // max_requests
        1, // window_duration_minutes (we'll simulate this)
    );

    let test_ip = "*************";

    // Use up the rate limit
    for _ in 1..=2 {
        let _status = rate_limit_service.increment_request_count(test_ip).await.unwrap();
    }

    // Should be rate limited now
    let is_limited = rate_limit_service.is_rate_limited(test_ip).await.unwrap();
    assert!(is_limited, "Should be rate limited after using up quota");

    // In a real scenario, we'd wait for the window to expire
    // For testing, we can verify the logic works by checking different IPs
    let different_ip = "*************";
    let is_limited_different = rate_limit_service.is_rate_limited(different_ip).await.unwrap();
    assert!(!is_limited_different, "Different IP should not be rate limited");
}

#[tokio::test]
async fn test_multiple_ips_independent_limits() {
    // Create test database connection
    let db = create_test_db().await;

    // Create rate limit service
    let rate_limit_service = RateLimitService::new(
        db,
        2, // max_requests
        1, // window_duration_minutes
    );

    let ip1 = "*************";
    let ip2 = "*************";

    // Use up rate limit for IP1
    for _ in 1..=2 {
        let _status = rate_limit_service.increment_request_count(ip1).await.unwrap();
    }

    // IP1 should be rate limited
    let is_limited_ip1 = rate_limit_service.is_rate_limited(ip1).await.unwrap();
    assert!(is_limited_ip1, "IP1 should be rate limited");

    // IP2 should still be allowed
    let is_limited_ip2 = rate_limit_service.is_rate_limited(ip2).await.unwrap();
    assert!(!is_limited_ip2, "IP2 should not be rate limited");

    // IP2 can still make requests
    let _status = rate_limit_service.increment_request_count(ip2).await.unwrap();
    let is_limited_ip2_after = rate_limit_service.is_rate_limited(ip2).await.unwrap();
    assert!(!is_limited_ip2_after, "IP2 should still not be rate limited after one request");
}


