# Server Configuration
PORT=3000

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/invoice_generator
DATABASE_MAX_CONNECTIONS=100
DATABASE_MIN_CONNECTIONS=5
DATABASE_CONNECT_TIMEOUT=8

# R2 Storage Configuration
R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
R2_ACCESS_KEY=your-access-key
R2_SECRET_KEY=your-secret-key
R2_BUCKET_NAME=invoice-pdfs
R2_REGION=auto

# Rate Limiting Configuration
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MINUTES=60

# PDF Configuration
PDF_RETENTION_DAYS=7
WKHTMLTOPDF_PATH=/usr/local/bin/wkhtmltopdf

# Security Configuration
MAX_REQUEST_SIZE=********
ENABLE_HSTS=true
ENABLE_CSP=true
REQUEST_TIMEOUT_SECONDS=30
ALLOWED_ORIGINS=*
ENABLE_SECURITY_HEADERS=true

# Monitoring and Logging Configuration
ENABLE_METRICS=true
ENABLE_TRACING=true
LOG_LEVEL=info
ENABLE_CORRELATION_IDS=true
ENABLE_REQUEST_LOGGING=true
METRICS_ENDPOINT_ENABLED=true

# Development/Production Environment
RUST_LOG=invoice_generator_api=info,tower_http=info
